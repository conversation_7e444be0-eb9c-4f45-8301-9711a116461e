[2025-06-16 16:29:54.227 +02:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/2 OPTIONS https://localhost:7012/progressHub/negotiate?negotiateVersion=1 - 204 null null 28.9674ms {"EventId":{"Id":2},"RequestId":"0HNDCS91EAQ13:00000001","RequestPath":"/progressHub/negotiate","ConnectionId":"0HNDCS91EAQ13"}
[2025-06-16 16:29:54.250 +02:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/2 POST https://localhost:7012/progressHub/negotiate?negotiateVersion=1 - null 0 {"EventId":{"Id":1},"RequestId":"0HNDCS91EAQ13:00000003","RequestPath":"/progressHub/negotiate","ConnectionId":"0HNDCS91EAQ13"}
[2025-06-16 16:29:54.282 +02:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/2 POST https://localhost:7012/progressHub/negotiate?negotiateVersion=1 - 200 316 application/json 33.1758ms {"EventId":{"Id":2},"RequestId":"0HNDCS91EAQ13:00000003","RequestPath":"/progressHub/negotiate","ConnectionId":"0HNDCS91EAQ13"}
[2025-06-16 16:29:54.322 +02:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7012/progressHub?id=dZx8YANUand-WQMhsBpO7w - null null {"EventId":{"Id":1},"RequestId":"0HNDCS91EAQ14:00000001","RequestPath":"/progressHub","ConnectionId":"0HNDCS91EAQ14"}
[2025-06-16 16:30:01.762 +02:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/2 OPTIONS https://localhost:7012/api/extraction/extract - null null {"EventId":{"Id":1},"RequestId":"0HNDCS91EAQ15:00000001","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:01.762 +02:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/2 OPTIONS https://localhost:7012/api/extraction/extract - 204 null null 0.6717ms {"EventId":{"Id":2},"RequestId":"0HNDCS91EAQ15:00000001","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:01.763 +02:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/2 POST https://localhost:7012/api/extraction/extract - application/json; charset=utf-8 356 {"EventId":{"Id":1},"RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:01.828 +02:00 INF] EmailAddressExtractor.Api.Controllers.ExtractionController: Received email extraction request with scope: "All", TenantId: 997f5ba7-7813-4620-8039-81ccc9df5981 {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:01.829 +02:00 INF] EmailAddressExtractor.Api.Controllers.ExtractionController: Starting email extraction with scope: "All" {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:01.831 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Starting email extraction process with scope: "All" for tenant: 997f5ba7-7813-4620-8039-81ccc9df5981 {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:01.831 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Starting email extraction process... {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:01.846 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: M365 configuration validated for tenant: 997f5ba7-7813-4620-8039-81ccc9df5981 {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:01.849 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Initializing resilient Graph client for tenant: 997f5ba7-7813-4620-8039-81ccc9df5981 {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:02.822 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully initialized resilient Graph client for tenant: 997f5ba7-7813-4620-8039-81ccc9df5981 {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:02.823 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Configured 0 company domains and 11 generic prefixes for filtering {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:02.825 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Fetching users to process for scope: "All" {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:02.825 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Fetching users to process... {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:02.825 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Fetching all users from tenant {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:02.825 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Fetching all users from tenant... {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:02.904 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Fetched 69 users from tenant {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:02.904 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Successfully fetched 69 users in 79.488ms {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:02.905 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Found 69 users to process {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:02.905 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 1/69: Aashay Madavi (<EMAIL>) {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:02.905 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 1 of 69: Aashay Madavi (<EMAIL>) {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:03.014 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Found 4 mail folders for Aashay Madavi {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:03.014 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 1 of 4: Archive {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:03.188 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 0 messages across 1 pages for folder AAMkADUyMmQyYTAzLTI2MzktNDVhMC1iZTkwLWRiZDVmYTEwMDFjNQAuAAAAAABE68n_ltSgSZWLnOgrKfMdAQDQi_vFf6o_TYzA33mRlEUFAAAAAAFAAAA= in 72.219ms {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:03.240 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 2 of 4: Conversation History {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:03.418 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 0 messages across 1 pages for folder AAMkADUyMmQyYTAzLTI2MzktNDVhMC1iZTkwLWRiZDVmYTEwMDFjNQAuAAAAAABE68n_ltSgSZWLnOgrKfMdAQDQi_vFf6o_TYzA33mRlEUFAAAAAAE4AAA= in 108.869ms {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:03.469 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 3 of 4: Inbox {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:04.732 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 85 messages across 2 pages for folder AAMkADUyMmQyYTAzLTI2MzktNDVhMC1iZTkwLWRiZDVmYTEwMDFjNQAuAAAAAABE68n_ltSgSZWLnOgrKfMdAQDQi_vFf6o_TYzA33mRlEUFAAAAAAEMAAA= in 709.885ms {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:05.217 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processed 85 emails from folder Inbox in 1747.17ms {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:05.218 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processed 85 emails from Inbox {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:05.269 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 4 of 4: Sent Items {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:05.532 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 9 messages across 1 pages for folder AAMkADUyMmQyYTAzLTI2MzktNDVhMC1iZTkwLWRiZDVmYTEwMDFjNQAuAAAAAABE68n_ltSgSZWLnOgrKfMdAQDQi_vFf6o_TYzA33mRlEUFAAAAAAEJAAA= in 138.443ms {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:05.587 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processed 9 emails from folder Sent Items in 318.064ms {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:05.587 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processed 9 emails from Sent Items {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:05.638 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Completed processing Aashay Madavi {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:05.690 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 2/69: Ajinkya Mahulkar (<EMAIL>) {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:05.690 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 2 of 69: Ajinkya Mahulkar (<EMAIL>) {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:05.775 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Found 4 mail folders for Ajinkya Mahulkar {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:05.775 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 1 of 4: Archive {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:05.925 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 0 messages across 1 pages for folder AAMkADg4YzlmNTRjLTUyNzItNGNiMS1hNWVlLWI0YjIwZDdhYTdkYwAuAAAAAAAPEjQN5GVfSq67ths3nhHTAQAbYnI5CsukT6h0dgrrdRwQAAAAAAFAAAA= in 74.561ms {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:05.976 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 2 of 4: Conversation History {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:06.132 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 0 messages across 1 pages for folder AAMkADg4YzlmNTRjLTUyNzItNGNiMS1hNWVlLWI0YjIwZDdhYTdkYwAuAAAAAAAPEjQN5GVfSq67ths3nhHTAQAbYnI5CsukT6h0dgrrdRwQAAAAAAE4AAA= in 73.479ms {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:06.184 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 3 of 4: Inbox {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:06.628 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 15 messages across 1 pages for folder AAMkADg4YzlmNTRjLTUyNzItNGNiMS1hNWVlLWI0YjIwZDdhYTdkYwAuAAAAAAAPEjQN5GVfSq67ths3nhHTAQAbYnI5CsukT6h0dgrrdRwQAAAAAAEMAAA= in 188.136ms {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:06.719 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processed 15 emails from folder Inbox in 534.998ms {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:06.719 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processed 15 emails from Inbox {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:06.771 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 4 of 4: Sent Items {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:07.055 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 5 messages across 1 pages for folder AAMkADg4YzlmNTRjLTUyNzItNGNiMS1hNWVlLWI0YjIwZDdhYTdkYwAuAAAAAAAPEjQN5GVfSq67ths3nhHTAQAbYnI5CsukT6h0dgrrdRwQAAAAAAEJAAA= in 139.323ms {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:07.082 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processed 5 emails from folder Sent Items in 310.692ms {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:07.082 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processed 5 emails from Sent Items {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:07.133 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Completed processing Ajinkya Mahulkar {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:07.185 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 3/69: akhileshj (akhileshj_heaptrace.net#EXT#@bifirm.onmicrosoft.com) {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:07.185 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 3 of 69: akhileshj (akhileshj_heaptrace.net#EXT#@bifirm.onmicrosoft.com) {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:07.610 +02:00 ERR] EmailAddressExtractor.Api.Services.ResilientGraphService: Failed to fetch mail folders for user 742182af-1cb8-47e0-b122-9b8a8905da2d after 424.596ms {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
Microsoft.Graph.Models.ODataErrors.ODataError: The mailbox is either inactive, soft-deleted, or is hosted on-premise.
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.ThrowIfFailedResponseAsync(HttpResponseMessage response, Dictionary`2 errorMapping, Activity activityForAttributes, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Graph.Users.Item.MailFolders.MailFoldersRequestBuilder.GetAsync(Action`1 requestConfiguration, CancellationToken cancellationToken)
   at EmailAddressExtractor.Api.Services.ResilientGraphService.<>c__DisplayClass8_0.<<GetMailFoldersAsync>b__0>d.MoveNext() in /Users/<USER>/dev/EmailAddressExtractor/EmailAddressExtractor.Api/Services/ResilientGraphService.cs:line 188
--- End of stack trace from previous location ---
   at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, CancellationToken cancellationToken, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
   at Polly.AsyncPolicy.ExecuteInternalAsync[TResult](Func`3 action, Context context, Boolean continueOnCapturedContext, CancellationToken cancellationToken)
   at EmailAddressExtractor.Api.Services.ResilientGraphService.GetMailFoldersAsync(String userId, CancellationToken cancellationToken) in /Users/<USER>/dev/EmailAddressExtractor/EmailAddressExtractor.Api/Services/ResilientGraphService.cs:line 186
[2025-06-16 16:30:17.014 +02:00 ERR] EmailAddressExtractor.Api.Services.GraphExtractionService: Error getting mail folders for user 742182af-1cb8-47e0-b122-9b8a8905da2d after 9802.259ms {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
Microsoft.Graph.Models.ODataErrors.ODataError: The mailbox is either inactive, soft-deleted, or is hosted on-premise.
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.ThrowIfFailedResponseAsync(HttpResponseMessage response, Dictionary`2 errorMapping, Activity activityForAttributes, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Graph.Users.Item.MailFolders.MailFoldersRequestBuilder.GetAsync(Action`1 requestConfiguration, CancellationToken cancellationToken)
   at EmailAddressExtractor.Api.Services.ResilientGraphService.<>c__DisplayClass8_0.<<GetMailFoldersAsync>b__0>d.MoveNext() in /Users/<USER>/dev/EmailAddressExtractor/EmailAddressExtractor.Api/Services/ResilientGraphService.cs:line 188
--- End of stack trace from previous location ---
   at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, CancellationToken cancellationToken, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
   at Polly.AsyncPolicy.ExecuteInternalAsync[TResult](Func`3 action, Context context, Boolean continueOnCapturedContext, CancellationToken cancellationToken)
   at EmailAddressExtractor.Api.Services.ResilientGraphService.GetMailFoldersAsync(String userId, CancellationToken cancellationToken) in /Users/<USER>/dev/EmailAddressExtractor/EmailAddressExtractor.Api/Services/ResilientGraphService.cs:line 186
   at EmailAddressExtractor.Api.Services.GraphExtractionService.GetMailFolders(String userId, CancellationToken cancellationToken) in /Users/<USER>/dev/EmailAddressExtractor/EmailAddressExtractor.Api/Services/GraphExtractionService.cs:line 322
[2025-06-16 16:30:17.016 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Error getting mail folders for user 742182af-1cb8-47e0-b122-9b8a8905da2d: The mailbox is either inactive, soft-deleted, or is hosted on-premise. {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:17.017 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No accessible mail folders found for akhileshj {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:17.068 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 4/69: Akshay Patil (<EMAIL>) {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:17.068 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 4 of 69: Akshay Patil (<EMAIL>) {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:17.177 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Found 4 mail folders for Akshay Patil {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:17.177 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 1 of 4: Archive {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:17.362 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 2 messages across 1 pages for folder AQMkADEyZjIwMmRhLTliZGUtNGFiMi1iMWJkLTlkMDM0NTM1ZDEzYQAuAAADuBPPAYrzm06Sx6CIwYhc5AEAi6_QzL1wnUaLLdahJGyVoAAAAgEUAAAA in 105.218ms {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:17.373 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processed 2 emails from folder Archive in 195.381ms {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:17.373 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processed 2 emails from Archive {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:17.423 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 2 of 4: Conversation History {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:17.561 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 0 messages across 1 pages for folder AQMkADEyZjIwMmRhLTliZGUtNGFiMi1iMWJkLTlkMDM0NTM1ZDEzYQAuAAADuBPPAYrzm06Sx6CIwYhc5AEAi6_QzL1wnUaLLdahJGyVoAAAAgEXAAAA in 70.922ms {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:30:17.612 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 3 of 4: Inbox {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:03.489 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 2883 messages across 58 pages for folder AQMkADEyZjIwMmRhLTliZGUtNGFiMi1iMWJkLTlkMDM0NTM1ZDEzYQAuAAADuBPPAYrzm06Sx6CIwYhc5AEAi6_QzL1wnUaLLdahJGyVoAAAAgEMAAAA in 45395.327ms {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:19.924 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processed 2883 emails from folder Inbox in 62311.614ms {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:19.924 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processed 2883 emails from Inbox {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:19.975 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 4 of 4: Sent Items {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:20.150 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 2 messages across 1 pages for folder AQMkADEyZjIwMmRhLTliZGUtNGFiMi1iMWJkLTlkMDM0NTM1ZDEzYQAuAAADuBPPAYrzm06Sx6CIwYhc5AEAi6_QzL1wnUaLLdahJGyVoAAAAgEJAAAA in 76.579ms {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:20.162 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processed 2 emails from folder Sent Items in 186.344ms {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:20.162 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processed 2 emails from Sent Items {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:20.213 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Completed processing Akshay Patil {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:20.265 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 5/69: Alexandra Hansson (<EMAIL>) {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:20.265 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 5 of 69: Alexandra Hansson (<EMAIL>) {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:20.364 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Found 10 mail folders for Alexandra Hansson {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:20.364 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 1 of 10: Arkiv {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:20.533 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 0 messages across 1 pages for folder AQMkADQ4N2U1ZWUAZi00YzRkLTQwZTUtYTNiMy04MjFjNDc0MmQ0MmUALgAAA4hOQdN0QhNHsYc9jc1uQx4BACj0hKxTGg1NmOmtt3tM7FkAAAIBQAAAAA== in 91.187ms {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:20.585 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 2 of 10: Borttagna objekt {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:20.889 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 6 messages across 1 pages for folder AQMkADQ4N2U1ZWUAZi00YzRkLTQwZTUtYTNiMy04MjFjNDc0MmQ0MmUALgAAA4hOQdN0QhNHsYc9jc1uQx4BACj0hKxTGg1NmOmtt3tM7FkAAAIBCgAAAA== in 109.687ms {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:20.924 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processed 6 emails from folder Borttagna objekt in 339.05ms {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:20.925 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processed 6 emails from Borttagna objekt {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:20.976 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 3 of 10: Inkorg {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:21.162 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 2 messages across 1 pages for folder AQMkADQ4N2U1ZWUAZi00YzRkLTQwZTUtYTNiMy04MjFjNDc0MmQ0MmUALgAAA4hOQdN0QhNHsYc9jc1uQx4BACj0hKxTGg1NmOmtt3tM7FkAAAIBDAAAAA== in 72.265ms {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:21.174 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processed 2 emails from folder Inkorg in 197.222ms {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:21.175 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processed 2 emails from Inkorg {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:21.225 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 4 of 10: Konversationshistorik {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:21.406 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 0 messages across 1 pages for folder AQMkADQ4N2U1ZWUAZi00YzRkLTQwZTUtYTNiMy04MjFjNDc0MmQ0MmUALgAAA4hOQdN0QhNHsYc9jc1uQx4BACj0hKxTGg1NmOmtt3tM7FkAAAIBOAAAAA== in 100.435ms {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:21.457 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 5 of 10: RSS-feeds {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:21.596 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 0 messages across 1 pages for folder AAMkADQ4N2U1ZWVmLTRjNGQtNDBlNS1hM2IzLTgyMWM0NzQyZDQyZQAuAAAAAACITkHTdEITR7GHPY3NbkMeAQAo9ISsUxoNTZjprbd7TOxZAAAAAbJGAAA= in 64.867ms {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:21.648 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 6 of 10: Skickat {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:21.781 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 0 messages across 1 pages for folder AQMkADQ4N2U1ZWUAZi00YzRkLTQwZTUtYTNiMy04MjFjNDc0MmQ0MmUALgAAA4hOQdN0QhNHsYc9jc1uQx4BACj0hKxTGg1NmOmtt3tM7FkAAAIBCQAAAA== in 62.593ms {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:21.833 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 7 of 10: Skräppost {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:22.015 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 0 messages across 1 pages for folder AQMkADQ4N2U1ZWUAZi00YzRkLTQwZTUtYTNiMy04MjFjNDc0MmQ0MmUALgAAA4hOQdN0QhNHsYc9jc1uQx4BACj0hKxTGg1NmOmtt3tM7FkAAAIBFwAAAA== in 110.799ms {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:22.067 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 8 of 10: Synkroniseringsproblem {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:22.200 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 0 messages across 1 pages for folder AQMkADQ4N2U1ZWUAZi00YzRkLTQwZTUtYTNiMy04MjFjNDc0MmQ0MmUALgAAA4hOQdN0QhNHsYc9jc1uQx4BACj0hKxTGg1NmOmtt3tM7FkAAAJh4gAAAA== in 63.244ms {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:22.251 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 9 of 10: Utkast {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:22.390 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 0 messages across 1 pages for folder AQMkADQ4N2U1ZWUAZi00YzRkLTQwZTUtYTNiMy04MjFjNDc0MmQ0MmUALgAAA4hOQdN0QhNHsYc9jc1uQx4BACj0hKxTGg1NmOmtt3tM7FkAAAIBDwAAAA== in 70.355ms {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:22.441 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 10 of 10: Utkorg {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:22.575 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 0 messages across 1 pages for folder AQMkADQ4N2U1ZWUAZi00YzRkLTQwZTUtYTNiMy04MjFjNDc0MmQ0MmUALgAAA4hOQdN0QhNHsYc9jc1uQx4BACj0hKxTGg1NmOmtt3tM7FkAAAIBCwAAAA== in 64.984ms {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:22.626 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Completed processing Alexandra Hansson {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:22.677 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 6/69: Amol Wani (<EMAIL>) {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:22.678 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 6 of 69: Amol Wani (<EMAIL>) {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:22.852 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Found 7 mail folders for Amol Wani {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:22.852 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 1 of 7: Archive {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:22.999 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 0 messages across 1 pages for folder AAMkADE5YjU5ZmZiLTIwMTgtNGU4MS04MDk1LTQ0M2U5ZGI2MDE3NwAuAAAAAACqIT9YWLXASKBuQHjLCwGSAQBo7EUY-BxtTYNm1xMdPqcmAAAAAAETAAA= in 73.926ms {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:23.050 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 2 of 7: Conversation History {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:23.262 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 0 messages across 1 pages for folder AAMkADE5YjU5ZmZiLTIwMTgtNGU4MS04MDk1LTQ0M2U5ZGI2MDE3NwAuAAAAAACqIT9YWLXASKBuQHjLCwGSAQBo7EUY-BxtTYNm1xMdPqcmAAAAAAEVAAA= in 142.194ms {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:23.314 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 3 of 7: Inbox {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:41.840 +02:00 WRN] EmailAddressExtractor.Api.Services.ResilientGraphService: Graph API call failed (attempt 1), retrying in 2431ms. Exception: The operation was canceled. {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:44.312 +02:00 WRN] EmailAddressExtractor.Api.Services.ResilientGraphService: Graph API call failed (attempt 2), retrying in 4632ms. Exception: A task was canceled. {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:49.021 +02:00 WRN] EmailAddressExtractor.Api.Services.ResilientGraphService: Graph API call failed (attempt 3), retrying in 8272ms. Exception: A task was canceled. {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:31:57.322 +02:00 WRN] EmailAddressExtractor.Api.Services.ResilientGraphService: Graph API call failed (attempt 4), retrying in 16148ms. Exception: A task was canceled. {"ActionId":"821fdc6a-55e1-4018-9457-6c259e71cdbd","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCS91EAQ15:00000003","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCS91EAQ15"}
[2025-06-16 16:37:21.212 +02:00 INF] : Starting Email Address Extractor API {}
[2025-06-16 16:37:27.559 +02:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/2 OPTIONS https://localhost:7012/progressHub/negotiate?negotiateVersion=1 - null null {"EventId":{"Id":1},"RequestId":"0HNDCSD8I05OH:00000001","RequestPath":"/progressHub/negotiate","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:27.588 +02:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/2 OPTIONS https://localhost:7012/progressHub/negotiate?negotiateVersion=1 - 204 null null 32.2342ms {"EventId":{"Id":2},"RequestId":"0HNDCSD8I05OH:00000001","RequestPath":"/progressHub/negotiate","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:27.594 +02:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/2 POST https://localhost:7012/progressHub/negotiate?negotiateVersion=1 - null 0 {"EventId":{"Id":1},"RequestId":"0HNDCSD8I05OH:00000003","RequestPath":"/progressHub/negotiate","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:27.606 +02:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/2 POST https://localhost:7012/progressHub/negotiate?negotiateVersion=1 - 200 316 application/json 12.048ms {"EventId":{"Id":2},"RequestId":"0HNDCSD8I05OH:00000003","RequestPath":"/progressHub/negotiate","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:27.663 +02:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7012/progressHub?id=Jj5hHald_PzqlS6IV1LXNw - null null {"EventId":{"Id":1},"RequestId":"0HNDCSD8I05OI:00000001","RequestPath":"/progressHub","ConnectionId":"0HNDCSD8I05OI"}
[2025-06-16 16:37:42.812 +02:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/2 OPTIONS https://localhost:7012/api/extraction/extract - null null {"EventId":{"Id":1},"RequestId":"0HNDCSD8I05OH:00000005","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:42.819 +02:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/2 OPTIONS https://localhost:7012/api/extraction/extract - 204 null null 8.0311ms {"EventId":{"Id":2},"RequestId":"0HNDCSD8I05OH:00000005","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:42.823 +02:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/2 POST https://localhost:7012/api/extraction/extract - application/json; charset=utf-8 356 {"EventId":{"Id":1},"RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:42.901 +02:00 INF] EmailAddressExtractor.Api.Controllers.ExtractionController: Received email extraction request with scope: "All", TenantId: 997f5ba7-7813-4620-8039-81ccc9df5981 {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:42.902 +02:00 INF] EmailAddressExtractor.Api.Controllers.ExtractionController: Starting email extraction with scope: "All" {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:42.904 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Starting email extraction process with scope: "All" for tenant: 997f5ba7-7813-4620-8039-81ccc9df5981 {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:42.905 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Starting email extraction process... {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:42.921 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: M365 configuration validated for tenant: 997f5ba7-7813-4620-8039-81ccc9df5981 {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:42.923 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Initializing resilient Graph client for tenant: 997f5ba7-7813-4620-8039-81ccc9df5981 {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:43.707 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully initialized resilient Graph client for tenant: 997f5ba7-7813-4620-8039-81ccc9df5981 {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:43.707 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Configured 0 company domains and 11 generic prefixes for filtering {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:43.709 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Fetching users to process for scope: "All" {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:43.709 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Fetching users to process... {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:43.709 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Fetching all users from tenant {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:43.710 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Fetching all users from tenant... {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:43.788 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Fetched 69 users from tenant {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:43.788 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Successfully fetched 69 users in 78.8ms {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:43.788 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Found 69 users to process {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:43.789 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 1/69: Aashay Madavi (<EMAIL>) {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:43.789 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 1 of 69: Aashay Madavi (<EMAIL>) {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:43.893 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Found 4 mail folders for Aashay Madavi {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:43.893 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 1 of 4: Archive {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:44.061 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 0 messages across 1 pages for folder AAMkADUyMmQyYTAzLTI2MzktNDVhMC1iZTkwLWRiZDVmYTEwMDFjNQAuAAAAAABE68n_ltSgSZWLnOgrKfMdAQDQi_vFf6o_TYzA33mRlEUFAAAAAAFAAAA= in 80.019ms {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:44.113 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 2 of 4: Conversation History {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:44.274 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 0 messages across 1 pages for folder AAMkADUyMmQyYTAzLTI2MzktNDVhMC1iZTkwLWRiZDVmYTEwMDFjNQAuAAAAAABE68n_ltSgSZWLnOgrKfMdAQDQi_vFf6o_TYzA33mRlEUFAAAAAAE4AAA= in 88.713ms {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:44.326 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 3 of 4: Inbox {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:45.583 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 85 messages across 2 pages for folder AAMkADUyMmQyYTAzLTI2MzktNDVhMC1iZTkwLWRiZDVmYTEwMDFjNQAuAAAAAABE68n_ltSgSZWLnOgrKfMdAQDQi_vFf6o_TYzA33mRlEUFAAAAAAEMAAA= in 1190.487ms {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:45.636 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processed 85 emails from folder Inbox in 1309.63ms {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:45.636 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processed 85 emails from Inbox {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:45.688 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 4 of 4: Sent Items {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:45.901 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 9 messages across 1 pages for folder AAMkADUyMmQyYTAzLTI2MzktNDVhMC1iZTkwLWRiZDVmYTEwMDFjNQAuAAAAAABE68n_ltSgSZWLnOgrKfMdAQDQi_vFf6o_TYzA33mRlEUFAAAAAAEJAAA= in 145.156ms {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:45.902 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processed 9 emails from folder Sent Items in 214.171ms {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:45.902 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processed 9 emails from Sent Items {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:45.954 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Completed processing Aashay Madavi {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:46.006 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 2/69: Ajinkya Mahulkar (<EMAIL>) {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:46.006 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 2 of 69: Ajinkya Mahulkar (<EMAIL>) {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:46.100 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Found 4 mail folders for Ajinkya Mahulkar {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:46.100 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 1 of 4: Archive {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:46.302 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 0 messages across 1 pages for folder AAMkADg4YzlmNTRjLTUyNzItNGNiMS1hNWVlLWI0YjIwZDdhYTdkYwAuAAAAAAAPEjQN5GVfSq67ths3nhHTAQAbYnI5CsukT6h0dgrrdRwQAAAAAAFAAAA= in 100.961ms {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:46.354 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 2 of 4: Conversation History {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:46.494 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 0 messages across 1 pages for folder AAMkADg4YzlmNTRjLTUyNzItNGNiMS1hNWVlLWI0YjIwZDdhYTdkYwAuAAAAAAAPEjQN5GVfSq67ths3nhHTAQAbYnI5CsukT6h0dgrrdRwQAAAAAAE4AAA= in 70.798ms {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:46.545 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 3 of 4: Inbox {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:47.116 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 15 messages across 1 pages for folder AAMkADg4YzlmNTRjLTUyNzItNGNiMS1hNWVlLWI0YjIwZDdhYTdkYwAuAAAAAAAPEjQN5GVfSq67ths3nhHTAQAbYnI5CsukT6h0dgrrdRwQAAAAAAEMAAA= in 452.555ms {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:47.122 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processed 15 emails from folder Inbox in 575.642ms {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:47.122 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processed 15 emails from Inbox {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:47.173 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 4 of 4: Sent Items {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:47.334 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 5 messages across 1 pages for folder AAMkADg4YzlmNTRjLTUyNzItNGNiMS1hNWVlLWI0YjIwZDdhYTdkYwAuAAAAAAAPEjQN5GVfSq67ths3nhHTAQAbYnI5CsukT6h0dgrrdRwQAAAAAAEJAAA= in 94.913ms {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:47.334 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processed 5 emails from folder Sent Items in 160.728ms {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:47.334 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processed 5 emails from Sent Items {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:47.385 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Completed processing Ajinkya Mahulkar {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:47.436 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 3/69: akhileshj (akhileshj_heaptrace.net#EXT#@bifirm.onmicrosoft.com) {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:47.437 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 3 of 69: akhileshj (akhileshj_heaptrace.net#EXT#@bifirm.onmicrosoft.com) {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
[2025-06-16 16:37:47.819 +02:00 ERR] EmailAddressExtractor.Api.Services.ResilientGraphService: Failed to fetch mail folders for user 742182af-1cb8-47e0-b122-9b8a8905da2d after 382.25ms {"ActionId":"adc7b598-455e-457d-bcb3-463030340f0e","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSD8I05OH:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSD8I05OH"}
Microsoft.Graph.Models.ODataErrors.ODataError: The mailbox is either inactive, soft-deleted, or is hosted on-premise.
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.ThrowIfFailedResponseAsync(HttpResponseMessage response, Dictionary`2 errorMapping, Activity activityForAttributes, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Graph.Users.Item.MailFolders.MailFoldersRequestBuilder.GetAsync(Action`1 requestConfiguration, CancellationToken cancellationToken)
   at EmailAddressExtractor.Api.Services.ResilientGraphService.<>c__DisplayClass8_0.<<GetMailFoldersAsync>b__0>d.MoveNext() in /Users/<USER>/dev/EmailAddressExtractor/EmailAddressExtractor.Api/Services/ResilientGraphService.cs:line 188
--- End of stack trace from previous location ---
   at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, CancellationToken cancellationToken, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
   at Polly.AsyncPolicy.ExecuteInternalAsync[TResult](Func`3 action, Context context, Boolean continueOnCapturedContext, CancellationToken cancellationToken)
   at EmailAddressExtractor.Api.Services.ResilientGraphService.GetMailFoldersAsync(String userId, CancellationToken cancellationToken) in /Users/<USER>/dev/EmailAddressExtractor/EmailAddressExtractor.Api/Services/ResilientGraphService.cs:line 186
[2025-06-16 16:44:53.858 +02:00 INF] : Starting Email Address Extractor API {}
[2025-06-16 16:45:01.077 +02:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/2 OPTIONS https://localhost:7012/progressHub/negotiate?negotiateVersion=1 - null null {"EventId":{"Id":1},"RequestId":"0HNDCSHFN1LM9:00000001","RequestPath":"/progressHub/negotiate","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:01.101 +02:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/2 OPTIONS https://localhost:7012/progressHub/negotiate?negotiateVersion=1 - 204 null null 26.8222ms {"EventId":{"Id":2},"RequestId":"0HNDCSHFN1LM9:00000001","RequestPath":"/progressHub/negotiate","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:01.111 +02:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/2 POST https://localhost:7012/progressHub/negotiate?negotiateVersion=1 - null 0 {"EventId":{"Id":1},"RequestId":"0HNDCSHFN1LM9:00000003","RequestPath":"/progressHub/negotiate","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:01.121 +02:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/2 POST https://localhost:7012/progressHub/negotiate?negotiateVersion=1 - 200 316 application/json 10.3151ms {"EventId":{"Id":2},"RequestId":"0HNDCSHFN1LM9:00000003","RequestPath":"/progressHub/negotiate","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:01.189 +02:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7012/progressHub?id=Xs3ZY_-0gkjQ3jzDGvLEUw - null null {"EventId":{"Id":1},"RequestId":"0HNDCSHFN1LMA:00000001","RequestPath":"/progressHub","ConnectionId":"0HNDCSHFN1LMA"}
[2025-06-16 16:45:38.049 +02:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/2 OPTIONS https://localhost:7012/api/extraction/extract - null null {"EventId":{"Id":1},"RequestId":"0HNDCSHFN1LM9:00000005","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:38.051 +02:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/2 OPTIONS https://localhost:7012/api/extraction/extract - 204 null null 10.3626ms {"EventId":{"Id":2},"RequestId":"0HNDCSHFN1LM9:00000005","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:38.058 +02:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/2 POST https://localhost:7012/api/extraction/extract - application/json; charset=utf-8 356 {"EventId":{"Id":1},"RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:38.145 +02:00 INF] EmailAddressExtractor.Api.Controllers.ExtractionController: Received email extraction request with scope: "All", TenantId: 997f5ba7-7813-4620-8039-81ccc9df5981 {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:38.145 +02:00 INF] EmailAddressExtractor.Api.Controllers.ExtractionController: Starting email extraction with scope: "All" {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:38.147 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Starting email extraction process with scope: "All" for tenant: 997f5ba7-7813-4620-8039-81ccc9df5981 {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:38.148 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Starting email extraction process... {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:38.163 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: M365 configuration validated for tenant: 997f5ba7-7813-4620-8039-81ccc9df5981 {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:38.165 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Initializing resilient Graph client for tenant: 997f5ba7-7813-4620-8039-81ccc9df5981 {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:38.859 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully initialized resilient Graph client for tenant: 997f5ba7-7813-4620-8039-81ccc9df5981 {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:38.859 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Configured 0 company domains and 11 generic prefixes for filtering {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:38.861 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Fetching users to process for scope: "All" {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:38.861 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Fetching users to process... {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:38.861 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Fetching all users from tenant {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:38.861 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Fetching all users from tenant... {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:38.949 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Fetched 69 users from tenant {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:38.950 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Successfully fetched 69 users in 88.483ms {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:38.950 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Found 69 users to process {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:38.950 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 1/69: Aashay Madavi (<EMAIL>) {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:38.950 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 1 of 69: Aashay Madavi (<EMAIL>) {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:39.065 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Found 4 mail folders for Aashay Madavi {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:39.065 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 1 of 4: Archive {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:39.256 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 0 messages across 1 pages for folder AAMkADUyMmQyYTAzLTI2MzktNDVhMC1iZTkwLWRiZDVmYTEwMDFjNQAuAAAAAABE68n_ltSgSZWLnOgrKfMdAQDQi_vFf6o_TYzA33mRlEUFAAAAAAFAAAA= in 89.541ms {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:39.308 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 2 of 4: Conversation History {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:39.489 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 0 messages across 1 pages for folder AAMkADUyMmQyYTAzLTI2MzktNDVhMC1iZTkwLWRiZDVmYTEwMDFjNQAuAAAAAABE68n_ltSgSZWLnOgrKfMdAQDQi_vFf6o_TYzA33mRlEUFAAAAAAE4AAA= in 86.916ms {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:39.541 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 3 of 4: Inbox {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:41.407 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 85 messages across 2 pages for folder AAMkADUyMmQyYTAzLTI2MzktNDVhMC1iZTkwLWRiZDVmYTEwMDFjNQAuAAAAAABE68n_ltSgSZWLnOgrKfMdAQDQi_vFf6o_TYzA33mRlEUFAAAAAAEMAAA= in 1794.58ms {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:41.461 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processed 85 emails from folder Inbox in 1920.141ms {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:41.461 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processed 85 emails from Inbox {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:41.512 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 4 of 4: Sent Items {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:41.896 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 9 messages across 1 pages for folder AAMkADUyMmQyYTAzLTI2MzktNDVhMC1iZTkwLWRiZDVmYTEwMDFjNQAuAAAAAABE68n_ltSgSZWLnOgrKfMdAQDQi_vFf6o_TYzA33mRlEUFAAAAAAEJAAA= in 317.776ms {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:41.896 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processed 9 emails from folder Sent Items in 384.164ms {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:41.897 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processed 9 emails from Sent Items {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:41.948 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Completed processing Aashay Madavi {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:42.001 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 2/69: Ajinkya Mahulkar (<EMAIL>) {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:42.001 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 2 of 69: Ajinkya Mahulkar (<EMAIL>) {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:42.082 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Found 4 mail folders for Ajinkya Mahulkar {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:42.082 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 1 of 4: Archive {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:42.237 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 0 messages across 1 pages for folder AAMkADg4YzlmNTRjLTUyNzItNGNiMS1hNWVlLWI0YjIwZDdhYTdkYwAuAAAAAAAPEjQN5GVfSq67ths3nhHTAQAbYnI5CsukT6h0dgrrdRwQAAAAAAFAAAA= in 71.491ms {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:42.289 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 2 of 4: Conversation History {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:42.454 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 0 messages across 1 pages for folder AAMkADg4YzlmNTRjLTUyNzItNGNiMS1hNWVlLWI0YjIwZDdhYTdkYwAuAAAAAAAPEjQN5GVfSq67ths3nhHTAQAbYnI5CsukT6h0dgrrdRwQAAAAAAE4AAA= in 100.993ms {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:42.506 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 3 of 4: Inbox {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:42.924 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 15 messages across 1 pages for folder AAMkADg4YzlmNTRjLTUyNzItNGNiMS1hNWVlLWI0YjIwZDdhYTdkYwAuAAAAAAAPEjQN5GVfSq67ths3nhHTAQAbYnI5CsukT6h0dgrrdRwQAAAAAAEMAAA= in 352.432ms {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:42.931 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processed 15 emails from folder Inbox in 424.747ms {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:42.931 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processed 15 emails from Inbox {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:42.982 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 4 of 4: Sent Items {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:43.172 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 5 messages across 1 pages for folder AAMkADg4YzlmNTRjLTUyNzItNGNiMS1hNWVlLWI0YjIwZDdhYTdkYwAuAAAAAAAPEjQN5GVfSq67ths3nhHTAQAbYnI5CsukT6h0dgrrdRwQAAAAAAEJAAA= in 126.137ms {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:43.172 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processed 5 emails from folder Sent Items in 189.726ms {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:43.173 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processed 5 emails from Sent Items {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:43.223 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Completed processing Ajinkya Mahulkar {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:43.274 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 3/69: akhileshj (akhileshj_heaptrace.net#EXT#@bifirm.onmicrosoft.com) {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:43.275 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 3 of 69: akhileshj (akhileshj_heaptrace.net#EXT#@bifirm.onmicrosoft.com) {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:43.664 +02:00 ERR] EmailAddressExtractor.Api.Services.ResilientGraphService: Failed to fetch mail folders for user 742182af-1cb8-47e0-b122-9b8a8905da2d after 388.821ms {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
Microsoft.Graph.Models.ODataErrors.ODataError: The mailbox is either inactive, soft-deleted, or is hosted on-premise.
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.ThrowIfFailedResponseAsync(HttpResponseMessage response, Dictionary`2 errorMapping, Activity activityForAttributes, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Graph.Users.Item.MailFolders.MailFoldersRequestBuilder.GetAsync(Action`1 requestConfiguration, CancellationToken cancellationToken)
   at EmailAddressExtractor.Api.Services.ResilientGraphService.<>c__DisplayClass8_0.<<GetMailFoldersAsync>b__0>d.MoveNext() in /Users/<USER>/dev/EmailAddressExtractor/EmailAddressExtractor.Api/Services/ResilientGraphService.cs:line 188
--- End of stack trace from previous location ---
   at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, CancellationToken cancellationToken, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
   at Polly.AsyncPolicy.ExecuteInternalAsync[TResult](Func`3 action, Context context, Boolean continueOnCapturedContext, CancellationToken cancellationToken)
   at EmailAddressExtractor.Api.Services.ResilientGraphService.GetMailFoldersAsync(String userId, CancellationToken cancellationToken) in /Users/<USER>/dev/EmailAddressExtractor/EmailAddressExtractor.Api/Services/ResilientGraphService.cs:line 186
[2025-06-16 16:45:48.266 +02:00 ERR] EmailAddressExtractor.Api.Services.GraphExtractionService: Error getting mail folders for user 742182af-1cb8-47e0-b122-9b8a8905da2d after 4991.132ms {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
Microsoft.Graph.Models.ODataErrors.ODataError: The mailbox is either inactive, soft-deleted, or is hosted on-premise.
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.ThrowIfFailedResponseAsync(HttpResponseMessage response, Dictionary`2 errorMapping, Activity activityForAttributes, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Graph.Users.Item.MailFolders.MailFoldersRequestBuilder.GetAsync(Action`1 requestConfiguration, CancellationToken cancellationToken)
   at EmailAddressExtractor.Api.Services.ResilientGraphService.<>c__DisplayClass8_0.<<GetMailFoldersAsync>b__0>d.MoveNext() in /Users/<USER>/dev/EmailAddressExtractor/EmailAddressExtractor.Api/Services/ResilientGraphService.cs:line 188
--- End of stack trace from previous location ---
   at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, CancellationToken cancellationToken, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
   at Polly.AsyncPolicy.ExecuteInternalAsync[TResult](Func`3 action, Context context, Boolean continueOnCapturedContext, CancellationToken cancellationToken)
   at EmailAddressExtractor.Api.Services.ResilientGraphService.GetMailFoldersAsync(String userId, CancellationToken cancellationToken) in /Users/<USER>/dev/EmailAddressExtractor/EmailAddressExtractor.Api/Services/ResilientGraphService.cs:line 186
   at EmailAddressExtractor.Api.Services.GraphExtractionService.GetMailFolders(String userId, CancellationToken cancellationToken) in /Users/<USER>/dev/EmailAddressExtractor/EmailAddressExtractor.Api/Services/GraphExtractionService.cs:line 322
[2025-06-16 16:45:48.270 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Error getting mail folders for user 742182af-1cb8-47e0-b122-9b8a8905da2d: The mailbox is either inactive, soft-deleted, or is hosted on-premise. {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:48.271 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No accessible mail folders found for akhileshj {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:48.322 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 4/69: Akshay Patil (<EMAIL>) {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:48.322 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 4 of 69: Akshay Patil (<EMAIL>) {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:48.417 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Found 4 mail folders for Akshay Patil {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:48.417 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 1 of 4: Archive {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:48.572 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 2 messages across 1 pages for folder AQMkADEyZjIwMmRhLTliZGUtNGFiMi1iMWJkLTlkMDM0NTM1ZDEzYQAuAAADuBPPAYrzm06Sx6CIwYhc5AEAi6_QzL1wnUaLLdahJGyVoAAAAgEUAAAA in 99.305ms {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:48.573 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processed 2 emails from folder Archive in 155.457ms {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:48.573 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processed 2 emails from Archive {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:48.625 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 2 of 4: Conversation History {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:48.747 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully fetched 0 messages across 1 pages for folder AQMkADEyZjIwMmRhLTliZGUtNGFiMi1iMWJkLTlkMDM0NTM1ZDEzYQAuAAADuBPPAYrzm06Sx6CIwYhc5AEAi6_QzL1wnUaLLdahJGyVoAAAAgEXAAAA in 65.535ms {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:45:48.799 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 3 of 4: Inbox {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:46:07.248 +02:00 WRN] EmailAddressExtractor.Api.Services.ResilientGraphService: Graph API call failed (attempt 1), retrying in 2419ms. Exception: The operation was canceled. {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:46:09.692 +02:00 WRN] EmailAddressExtractor.Api.Services.ResilientGraphService: Graph API call failed (attempt 2), retrying in 4724ms. Exception: A task was canceled. {"ActionId":"8583e59d-97bd-4119-8ed0-c34eb51810c8","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSHFN1LM9:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSHFN1LM9"}
[2025-06-16 16:49:35.850 +02:00 INF] : Starting Email Address Extractor API {}
