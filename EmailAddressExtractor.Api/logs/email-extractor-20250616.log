[2025-06-16 16:58:51.643 +02:00 INF] : Starting Email Address Extractor API {}
[2025-06-16 16:59:00.073 +02:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/2 OPTIONS https://localhost:7012/progressHub/negotiate?negotiateVersion=1 - null null {"EventId":{"Id":1},"RequestId":"0HNDCSP9NQS5F:00000001","RequestPath":"/progressHub/negotiate","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:00.155 +02:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/2 OPTIONS https://localhost:7012/progressHub/negotiate?negotiateVersion=1 - 204 null null 89.3598ms {"EventId":{"Id":2},"RequestId":"0HNDCSP9NQS5F:00000001","RequestPath":"/progressHub/negotiate","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:00.160 +02:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/2 POST https://localhost:7012/progressHub/negotiate?negotiateVersion=1 - null 0 {"EventId":{"Id":1},"RequestId":"0HNDCSP9NQS5F:00000003","RequestPath":"/progressHub/negotiate","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:00.185 +02:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/2 POST https://localhost:7012/progressHub/negotiate?negotiateVersion=1 - 200 316 application/json 23.0456ms {"EventId":{"Id":2},"RequestId":"0HNDCSP9NQS5F:00000003","RequestPath":"/progressHub/negotiate","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:00.225 +02:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/1.1 GET https://localhost:7012/progressHub?id=i7XL04l5FdFCEHbSXsqGbA - null null {"EventId":{"Id":1},"RequestId":"0HNDCSP9NQS5G:00000001","RequestPath":"/progressHub","ConnectionId":"0HNDCSP9NQS5G"}
[2025-06-16 16:59:18.805 +02:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/2 OPTIONS https://localhost:7012/api/extraction/extract - null null {"EventId":{"Id":1},"RequestId":"0HNDCSP9NQS5F:00000005","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:18.806 +02:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/2 OPTIONS https://localhost:7012/api/extraction/extract - 204 null null 1.051ms {"EventId":{"Id":2},"RequestId":"0HNDCSP9NQS5F:00000005","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:18.807 +02:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request starting HTTP/2 POST https://localhost:7012/api/extraction/extract - application/json; charset=utf-8 356 {"EventId":{"Id":1},"RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:18.869 +02:00 INF] EmailAddressExtractor.Api.Controllers.ExtractionController: Received email extraction request with scope: "All", TenantId: 997f5ba7-7813-4620-8039-81ccc9df5981 {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:18.869 +02:00 INF] EmailAddressExtractor.Api.Controllers.ExtractionController: Starting email extraction with scope: "All" {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:18.872 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Starting email extraction process with scope: "All" for tenant: 997f5ba7-7813-4620-8039-81ccc9df5981 {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:18.872 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Starting email extraction process... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:18.884 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: M365 configuration validated for tenant: 997f5ba7-7813-4620-8039-81ccc9df5981 {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:18.886 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Initializing resilient Graph client for tenant: 997f5ba7-7813-4620-8039-81ccc9df5981 {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:19.504 +02:00 INF] EmailAddressExtractor.Api.Services.ResilientGraphService: Successfully initialized resilient Graph client for tenant: 997f5ba7-7813-4620-8039-81ccc9df5981 {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:19.505 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Configured 0 company domains and 11 generic prefixes for filtering {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:19.507 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Fetching users to process for scope: "All" {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:19.507 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Fetching users to process... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:19.507 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Fetching all users from tenant {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:19.507 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Fetching all users from tenant... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:19.588 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Fetched 69 users from tenant {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:19.589 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Successfully fetched 69 users in 81.963ms {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:19.589 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Found 69 users to process {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:19.589 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 1/69: Aashay Madavi (<EMAIL>) {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:19.589 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 1 of 69: Aashay Madavi (<EMAIL>) {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:19.719 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Found 4 mail folders for Aashay Madavi {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:19.719 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 1 of 4: Archive {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:19.723 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Archive... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:19.794 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Archive {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:19.845 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 2 of 4: Conversation History {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:19.846 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Conversation History... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:19.938 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Conversation History {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:19.989 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 3 of 4: Inbox {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:19.991 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Inbox... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:20.092 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Inbox {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:20.144 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 4 of 4: Sent Items {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:20.144 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Sent Items... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:20.238 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Sent Items {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:20.289 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Completed processing Aashay Madavi {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:20.491 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 2/69: Ajinkya Mahulkar (<EMAIL>) {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:20.491 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 2 of 69: Ajinkya Mahulkar (<EMAIL>) {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:20.601 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Found 4 mail folders for Ajinkya Mahulkar {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:20.601 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 1 of 4: Archive {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:20.601 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Archive... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:20.663 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Archive {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:20.714 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 2 of 4: Conversation History {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:20.715 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Conversation History... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:20.794 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Conversation History {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:20.845 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 3 of 4: Inbox {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:20.846 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Inbox... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:20.910 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Inbox {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:20.962 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 4 of 4: Sent Items {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:20.962 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Sent Items... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:21.050 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Sent Items {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:21.101 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Completed processing Ajinkya Mahulkar {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:21.302 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 3/69: akhileshj (akhileshj_heaptrace.net#EXT#@bifirm.onmicrosoft.com) {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:21.302 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 3 of 69: akhileshj (akhileshj_heaptrace.net#EXT#@bifirm.onmicrosoft.com) {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:21.368 +02:00 ERR] EmailAddressExtractor.Api.Services.ResilientGraphService: Failed to fetch mail folders for user 742182af-1cb8-47e0-b122-9b8a8905da2d after 65.841ms {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
Microsoft.Graph.Models.ODataErrors.ODataError: The mailbox is either inactive, soft-deleted, or is hosted on-premise.
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.ThrowIfFailedResponseAsync(HttpResponseMessage response, Dictionary`2 errorMapping, Activity activityForAttributes, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Graph.Users.Item.MailFolders.MailFoldersRequestBuilder.GetAsync(Action`1 requestConfiguration, CancellationToken cancellationToken)
   at EmailAddressExtractor.Api.Services.ResilientGraphService.<>c__DisplayClass10_0.<<GetMailFoldersAsync>b__0>d.MoveNext() in /Users/<USER>/dev/EmailAddressExtractor/EmailAddressExtractor.Api/Services/ResilientGraphService.cs:line 194
--- End of stack trace from previous location ---
   at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, CancellationToken cancellationToken, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
   at Polly.AsyncPolicy.ExecuteInternalAsync[TResult](Func`3 action, Context context, Boolean continueOnCapturedContext, CancellationToken cancellationToken)
   at EmailAddressExtractor.Api.Services.ResilientGraphService.GetMailFoldersAsync(String userId, CancellationToken cancellationToken) in /Users/<USER>/dev/EmailAddressExtractor/EmailAddressExtractor.Api/Services/ResilientGraphService.cs:line 192
[2025-06-16 16:59:21.375 +02:00 ERR] EmailAddressExtractor.Api.Services.GraphExtractionService: Error getting mail folders for user 742182af-1cb8-47e0-b122-9b8a8905da2d after 72.834ms {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
Microsoft.Graph.Models.ODataErrors.ODataError: The mailbox is either inactive, soft-deleted, or is hosted on-premise.
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.ThrowIfFailedResponseAsync(HttpResponseMessage response, Dictionary`2 errorMapping, Activity activityForAttributes, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Graph.Users.Item.MailFolders.MailFoldersRequestBuilder.GetAsync(Action`1 requestConfiguration, CancellationToken cancellationToken)
   at EmailAddressExtractor.Api.Services.ResilientGraphService.<>c__DisplayClass10_0.<<GetMailFoldersAsync>b__0>d.MoveNext() in /Users/<USER>/dev/EmailAddressExtractor/EmailAddressExtractor.Api/Services/ResilientGraphService.cs:line 194
--- End of stack trace from previous location ---
   at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, CancellationToken cancellationToken, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
   at Polly.AsyncPolicy.ExecuteInternalAsync[TResult](Func`3 action, Context context, Boolean continueOnCapturedContext, CancellationToken cancellationToken)
   at EmailAddressExtractor.Api.Services.ResilientGraphService.GetMailFoldersAsync(String userId, CancellationToken cancellationToken) in /Users/<USER>/dev/EmailAddressExtractor/EmailAddressExtractor.Api/Services/ResilientGraphService.cs:line 192
   at EmailAddressExtractor.Api.Services.GraphExtractionService.GetMailFolders(String userId, CancellationToken cancellationToken) in /Users/<USER>/dev/EmailAddressExtractor/EmailAddressExtractor.Api/Services/GraphExtractionService.cs:line 323
[2025-06-16 16:59:21.376 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Error getting mail folders for user 742182af-1cb8-47e0-b122-9b8a8905da2d: The mailbox is either inactive, soft-deleted, or is hosted on-premise. {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:21.376 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No accessible mail folders found for akhileshj {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:21.577 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 4/69: Akshay Patil (<EMAIL>) {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:21.578 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 4 of 69: Akshay Patil (<EMAIL>) {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:21.646 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Found 4 mail folders for Akshay Patil {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:21.647 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 1 of 4: Archive {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:21.647 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Archive... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:21.703 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Archive {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:21.754 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 2 of 4: Conversation History {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:21.754 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Conversation History... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:21.812 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Conversation History {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:21.863 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 3 of 4: Inbox {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:21.863 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Inbox... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:21.921 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Inbox {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:21.972 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 4 of 4: Sent Items {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:21.972 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Sent Items... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:22.087 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Sent Items {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:22.140 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Completed processing Akshay Patil {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:22.340 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 5/69: Alexandra Hansson (<EMAIL>) {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:22.341 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 5 of 69: Alexandra Hansson (<EMAIL>) {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:23.243 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Found 10 mail folders for Alexandra Hansson {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:23.243 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 1 of 10: Arkiv {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:23.244 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Arkiv... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:23.300 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Arkiv {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:23.352 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 2 of 10: Borttagna objekt {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:23.352 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Borttagna objekt... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:23.464 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Borttagna objekt {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:23.516 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 3 of 10: Inkorg {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:23.516 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Inkorg... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:23.588 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Inkorg {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:23.639 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 4 of 10: Konversationshistorik {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:23.639 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Konversationshistorik... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:23.771 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Konversationshistorik {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:23.822 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 5 of 10: RSS-feeds {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:23.822 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for RSS-feeds... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:23.880 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in RSS-feeds {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:23.931 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 6 of 10: Skickat {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:23.931 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Skickat... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:23.994 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Skickat {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:24.045 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 7 of 10: Skräppost {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:24.045 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Skräppost... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:24.141 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Skräppost {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:24.192 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 8 of 10: Synkroniseringsproblem {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:24.193 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Synkroniseringsproblem... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:24.273 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Synkroniseringsproblem {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:24.324 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 9 of 10: Utkast {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:24.324 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Utkast... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:24.449 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Utkast {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:24.500 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 10 of 10: Utkorg {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:24.500 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Utkorg... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:24.563 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Utkorg {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:24.614 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Completed processing Alexandra Hansson {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:24.666 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 6/69: Amol Wani (<EMAIL>) {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:24.666 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 6 of 69: Amol Wani (<EMAIL>) {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:24.772 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Found 7 mail folders for Amol Wani {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:24.773 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 1 of 7: Archive {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:24.773 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Archive... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:24.839 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Archive {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:24.890 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 2 of 7: Conversation History {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:24.890 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Conversation History... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:25.058 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Conversation History {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:25.109 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 3 of 7: Inbox {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:25.110 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Inbox... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:25.174 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Inbox {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:25.234 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 4 of 7: Meta {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:25.235 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Meta... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:25.297 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Meta {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:25.348 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 5 of 7: RSS Feeds {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:25.348 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for RSS Feeds... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:25.451 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in RSS Feeds {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:25.503 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 6 of 7: Sent Items {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:25.503 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Sent Items... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:25.618 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Sent Items {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:25.672 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 7 of 7: Sync Issues {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:25.673 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Sync Issues... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:25.936 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Sync Issues {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:26.017 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Completed processing Amol Wani {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:26.097 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 7/69: Anders Wåglund (<EMAIL>) {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:26.097 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 7 of 69: Anders Wåglund (<EMAIL>) {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:26.201 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Found 8 mail folders for Anders Wåglund {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:26.203 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 1 of 8: [Gmail] {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:26.203 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for [Gmail]... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:26.264 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in [Gmail] {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:26.315 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 2 of 8: Archive {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:26.315 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Archive... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:26.439 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Archive {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:26.491 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 3 of 8: Arkiv {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:26.491 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Arkiv... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:26.561 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Arkiv {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:26.612 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 4 of 8: Conversation History {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:26.612 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Conversation History... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:26.703 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Conversation History {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:26.754 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 5 of 8: Inbox {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:26.755 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Inbox... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:26.842 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Inbox {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:26.905 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 6 of 8: RSS Subscriptions {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:26.905 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for RSS Subscriptions... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:26.966 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in RSS Subscriptions {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:27.017 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 7 of 8: Sent Items {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:27.017 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Sent Items... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:27.076 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Sent Items {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:27.127 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 8 of 8: Sync Issues {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:27.127 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Sync Issues... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:27.191 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Sync Issues {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:27.242 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Completed processing Anders Wåglund {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:27.299 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 8/69: Andreas Ling (<EMAIL>) {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:27.299 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 8 of 69: Andreas Ling (<EMAIL>) {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:27.374 +02:00 ERR] EmailAddressExtractor.Api.Services.ResilientGraphService: Failed to fetch mail folders for user 1c5137ab-cf49-4cd1-a61a-420af176c71c after 74.8ms {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
Microsoft.Graph.Models.ODataErrors.ODataError: The mailbox is either inactive, soft-deleted, or is hosted on-premise.
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.ThrowIfFailedResponseAsync(HttpResponseMessage response, Dictionary`2 errorMapping, Activity activityForAttributes, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Graph.Users.Item.MailFolders.MailFoldersRequestBuilder.GetAsync(Action`1 requestConfiguration, CancellationToken cancellationToken)
   at EmailAddressExtractor.Api.Services.ResilientGraphService.<>c__DisplayClass10_0.<<GetMailFoldersAsync>b__0>d.MoveNext() in /Users/<USER>/dev/EmailAddressExtractor/EmailAddressExtractor.Api/Services/ResilientGraphService.cs:line 194
--- End of stack trace from previous location ---
   at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, CancellationToken cancellationToken, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
   at Polly.AsyncPolicy.ExecuteInternalAsync[TResult](Func`3 action, Context context, Boolean continueOnCapturedContext, CancellationToken cancellationToken)
   at EmailAddressExtractor.Api.Services.ResilientGraphService.GetMailFoldersAsync(String userId, CancellationToken cancellationToken) in /Users/<USER>/dev/EmailAddressExtractor/EmailAddressExtractor.Api/Services/ResilientGraphService.cs:line 192
[2025-06-16 16:59:27.377 +02:00 ERR] EmailAddressExtractor.Api.Services.GraphExtractionService: Error getting mail folders for user 1c5137ab-cf49-4cd1-a61a-420af176c71c after 77.019ms {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
Microsoft.Graph.Models.ODataErrors.ODataError: The mailbox is either inactive, soft-deleted, or is hosted on-premise.
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.ThrowIfFailedResponseAsync(HttpResponseMessage response, Dictionary`2 errorMapping, Activity activityForAttributes, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Graph.Users.Item.MailFolders.MailFoldersRequestBuilder.GetAsync(Action`1 requestConfiguration, CancellationToken cancellationToken)
   at EmailAddressExtractor.Api.Services.ResilientGraphService.<>c__DisplayClass10_0.<<GetMailFoldersAsync>b__0>d.MoveNext() in /Users/<USER>/dev/EmailAddressExtractor/EmailAddressExtractor.Api/Services/ResilientGraphService.cs:line 194
--- End of stack trace from previous location ---
   at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, CancellationToken cancellationToken, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
   at Polly.AsyncPolicy.ExecuteInternalAsync[TResult](Func`3 action, Context context, Boolean continueOnCapturedContext, CancellationToken cancellationToken)
   at EmailAddressExtractor.Api.Services.ResilientGraphService.GetMailFoldersAsync(String userId, CancellationToken cancellationToken) in /Users/<USER>/dev/EmailAddressExtractor/EmailAddressExtractor.Api/Services/ResilientGraphService.cs:line 192
   at EmailAddressExtractor.Api.Services.GraphExtractionService.GetMailFolders(String userId, CancellationToken cancellationToken) in /Users/<USER>/dev/EmailAddressExtractor/EmailAddressExtractor.Api/Services/GraphExtractionService.cs:line 323
[2025-06-16 16:59:27.378 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Error getting mail folders for user 1c5137ab-cf49-4cd1-a61a-420af176c71c: The mailbox is either inactive, soft-deleted, or is hosted on-premise. {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:27.378 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No accessible mail folders found for Andreas Ling {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:27.579 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 9/69: Andreas Wallenberg (<EMAIL>) {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:27.579 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 9 of 69: Andreas Wallenberg (<EMAIL>) {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:27.659 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Found 16 mail folders for Andreas Wallenberg {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:27.659 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 1 of 16: Archive {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:27.659 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Archive... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:27.723 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Archive {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:27.773 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 2 of 16: Arkiv {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:27.773 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Arkiv... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:27.840 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Arkiv {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:27.891 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 3 of 16: Arkiverat {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:27.892 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Arkiverat... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:27.958 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Arkiverat {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:28.009 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 4 of 16: Borttagna objekt {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:28.010 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Borttagna objekt... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:28.081 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Borttagna objekt {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:28.132 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 5 of 16: Inkorg {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:28.132 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Inkorg... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:28.198 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Inkorg {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:28.249 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 6 of 16: Junk {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:28.249 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Junk... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:28.317 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Junk {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:28.367 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 7 of 16: Konversationshistorik {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:28.367 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Konversationshistorik... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:28.463 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Konversationshistorik {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:28.515 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 8 of 16: Mailarkiv {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:28.515 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Mailarkiv... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:28.582 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Mailarkiv {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:28.631 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 9 of 16: Övrig e-post {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:28.631 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Övrig e-post... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:28.705 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Övrig e-post {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:28.756 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 10 of 16: Privat {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:28.756 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Privat... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:28.820 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Privat {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:28.871 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 11 of 16: RSS-prenumerationer {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:28.872 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for RSS-prenumerationer... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:28.935 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in RSS-prenumerationer {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:28.986 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 12 of 16: Skickat {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:28.987 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Skickat... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:29.067 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Skickat {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:29.119 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 13 of 16: Skräppost {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:29.119 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Skräppost... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:29.189 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Skräppost {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:29.239 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 14 of 16: Synkroniseringsproblem {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:29.239 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Synkroniseringsproblem... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:29.327 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Synkroniseringsproblem {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:29.379 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 15 of 16: Utkast {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:29.379 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Utkast... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:29.467 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Utkast {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:29.519 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 16 of 16: Utkorg {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:29.520 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Utkorg... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:29.624 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Utkorg {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:29.674 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Completed processing Andreas Wallenberg {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:29.726 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 10/69: Anna Wallner (<EMAIL>) {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:29.727 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 10 of 69: Anna Wallner (<EMAIL>) {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:29.857 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Found 8 mail folders for Anna Wallner {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:29.857 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 1 of 8: Arkiv {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:29.857 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Arkiv... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:29.981 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Arkiv {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:30.031 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 2 of 8: Borttagna objekt {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:30.031 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Borttagna objekt... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:30.110 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Borttagna objekt {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:30.162 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 3 of 8: Inkorg {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:30.162 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Inkorg... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:30.233 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Inkorg {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:30.284 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 4 of 8: Konversationshistorik {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:30.284 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Konversationshistorik... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:30.350 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Konversationshistorik {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:30.402 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 5 of 8: Skickat {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:30.402 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Skickat... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:30.467 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Skickat {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:30.518 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 6 of 8: Skräppost {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:30.518 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Skräppost... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:30.579 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Skräppost {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:30.631 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 7 of 8: Utkast {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:30.631 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Utkast... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:30.714 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Utkast {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:30.766 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 8 of 8: Utkorg {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:30.766 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Utkorg... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:30.863 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Utkorg {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:30.914 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Completed processing Anna Wallner {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:30.965 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 11/69: Atharva Pethkar (<EMAIL>) {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:30.965 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 11 of 69: Atharva Pethkar (<EMAIL>) {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:31.060 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Found 4 mail folders for Atharva Pethkar {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:31.060 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 1 of 4: Archive {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:31.060 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Archive... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:31.145 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Archive {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:31.197 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 2 of 4: Conversation History {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:31.197 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Conversation History... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:31.261 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Conversation History {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:31.312 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 3 of 4: Inbox {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:31.313 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Inbox... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:31.380 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Inbox {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:31.432 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 4 of 4: Sent Items {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:31.432 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Sent Items... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:31.494 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Sent Items {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:31.546 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Completed processing Atharva Pethkar {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:31.748 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 12/69: Boka demo med mig (<EMAIL>) {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:31.749 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 12 of 69: Boka demo med mig (<EMAIL>) {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:32.723 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Found 8 mail folders for Boka demo med mig {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:32.724 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 1 of 8: Arkiv {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:32.724 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Arkiv... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:38.391 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Arkiv {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:38.442 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 2 of 8: Borttagna objekt {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:38.442 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Borttagna objekt... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:43.820 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Borttagna objekt {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:43.871 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 3 of 8: Inkorg {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:43.872 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Inkorg... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:49.186 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Inkorg {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:49.238 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 4 of 8: Konversationshistorik {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:49.238 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Konversationshistorik... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:49.407 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Konversationshistorik {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:49.458 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 5 of 8: Skickat {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:49.458 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Skickat... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:49.554 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Skickat {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:49.605 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 6 of 8: Skräppost {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:49.605 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Skräppost... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:49.669 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Skräppost {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:49.721 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 7 of 8: Utkast {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:49.721 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Utkast... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:49.833 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Utkast {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:49.884 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 8 of 8: Utkorg {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:49.885 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Utkorg... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:49.956 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Utkorg {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:50.008 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Completed processing Boka demo med mig {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:50.059 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 13/69: Camilla Lundgren (<EMAIL>) {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:50.060 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 13 of 69: Camilla Lundgren (<EMAIL>) {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:50.162 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Found 8 mail folders for Camilla Lundgren {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:50.162 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 1 of 8: Arkiv {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:50.162 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Arkiv... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:50.230 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Arkiv {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:50.280 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 2 of 8: Borttagna objekt {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:50.280 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Borttagna objekt... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:50.363 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Borttagna objekt {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:50.414 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 3 of 8: Inkorg {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:50.415 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Inkorg... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:50.477 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Inkorg {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:50.528 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 4 of 8: Konversationshistorik {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:50.528 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Konversationshistorik... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:50.588 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Konversationshistorik {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:50.640 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 5 of 8: Skickat {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:50.640 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Skickat... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:50.708 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Skickat {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:50.760 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 6 of 8: Skräppost {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:50.760 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Skräppost... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:50.859 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Skräppost {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:50.911 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 7 of 8: Utkast {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:50.911 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Utkast... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:50.974 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Utkast {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:51.026 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 8 of 8: Utkorg {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:51.026 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Utkorg... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:51.090 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Utkorg {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:51.141 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Completed processing Camilla Lundgren {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:51.193 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 14/69: Daniel Dani (<EMAIL>) {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:51.193 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 14 of 69: Daniel Dani (<EMAIL>) {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:51.273 +02:00 ERR] EmailAddressExtractor.Api.Services.ResilientGraphService: Failed to fetch mail folders for user 681a04a5-3a01-4f8e-8e13-6b9783849090 after 79.578ms {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
Microsoft.Graph.Models.ODataErrors.ODataError: The mailbox is either inactive, soft-deleted, or is hosted on-premise.
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.ThrowIfFailedResponseAsync(HttpResponseMessage response, Dictionary`2 errorMapping, Activity activityForAttributes, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Graph.Users.Item.MailFolders.MailFoldersRequestBuilder.GetAsync(Action`1 requestConfiguration, CancellationToken cancellationToken)
   at EmailAddressExtractor.Api.Services.ResilientGraphService.<>c__DisplayClass10_0.<<GetMailFoldersAsync>b__0>d.MoveNext() in /Users/<USER>/dev/EmailAddressExtractor/EmailAddressExtractor.Api/Services/ResilientGraphService.cs:line 194
--- End of stack trace from previous location ---
   at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, CancellationToken cancellationToken, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
   at Polly.AsyncPolicy.ExecuteInternalAsync[TResult](Func`3 action, Context context, Boolean continueOnCapturedContext, CancellationToken cancellationToken)
   at EmailAddressExtractor.Api.Services.ResilientGraphService.GetMailFoldersAsync(String userId, CancellationToken cancellationToken) in /Users/<USER>/dev/EmailAddressExtractor/EmailAddressExtractor.Api/Services/ResilientGraphService.cs:line 192
[2025-06-16 16:59:51.281 +02:00 ERR] EmailAddressExtractor.Api.Services.GraphExtractionService: Error getting mail folders for user 681a04a5-3a01-4f8e-8e13-6b9783849090 after 87.945ms {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
Microsoft.Graph.Models.ODataErrors.ODataError: The mailbox is either inactive, soft-deleted, or is hosted on-premise.
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.ThrowIfFailedResponseAsync(HttpResponseMessage response, Dictionary`2 errorMapping, Activity activityForAttributes, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Kiota.Http.HttpClientLibrary.HttpClientRequestAdapter.SendAsync[ModelType](RequestInformation requestInfo, ParsableFactory`1 factory, Dictionary`2 errorMapping, CancellationToken cancellationToken)
   at Microsoft.Graph.Users.Item.MailFolders.MailFoldersRequestBuilder.GetAsync(Action`1 requestConfiguration, CancellationToken cancellationToken)
   at EmailAddressExtractor.Api.Services.ResilientGraphService.<>c__DisplayClass10_0.<<GetMailFoldersAsync>b__0>d.MoveNext() in /Users/<USER>/dev/EmailAddressExtractor/EmailAddressExtractor.Api/Services/ResilientGraphService.cs:line 194
--- End of stack trace from previous location ---
   at Polly.Retry.AsyncRetryEngine.ImplementationAsync[TResult](Func`3 action, Context context, ExceptionPredicates shouldRetryExceptionPredicates, ResultPredicates`1 shouldRetryResultPredicates, Func`5 onRetryAsync, CancellationToken cancellationToken, Int32 permittedRetryCount, IEnumerable`1 sleepDurationsEnumerable, Func`4 sleepDurationProvider, Boolean continueOnCapturedContext)
   at Polly.AsyncPolicy.ExecuteInternalAsync[TResult](Func`3 action, Context context, Boolean continueOnCapturedContext, CancellationToken cancellationToken)
   at EmailAddressExtractor.Api.Services.ResilientGraphService.GetMailFoldersAsync(String userId, CancellationToken cancellationToken) in /Users/<USER>/dev/EmailAddressExtractor/EmailAddressExtractor.Api/Services/ResilientGraphService.cs:line 192
   at EmailAddressExtractor.Api.Services.GraphExtractionService.GetMailFolders(String userId, CancellationToken cancellationToken) in /Users/<USER>/dev/EmailAddressExtractor/EmailAddressExtractor.Api/Services/GraphExtractionService.cs:line 323
[2025-06-16 16:59:51.282 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Error getting mail folders for user 681a04a5-3a01-4f8e-8e13-6b9783849090: The mailbox is either inactive, soft-deleted, or is hosted on-premise. {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:51.282 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No accessible mail folders found for Daniel Dani {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:51.483 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 15/69: Dev (<EMAIL>) {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:51.484 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 15 of 69: Dev (<EMAIL>) {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:51.582 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Found 4 mail folders for Dev {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:51.582 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 1 of 4: Archive {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:51.583 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Archive... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:51.644 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Archive {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:51.695 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 2 of 4: Conversation History {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:51.695 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Conversation History... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:51.759 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Conversation History {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:51.810 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 3 of 4: Inbox {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:51.811 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Inbox... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:51.938 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Inbox {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:51.990 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 4 of 4: Sent Items {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:51.990 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Sent Items... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:52.081 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Sent Items {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:52.132 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Completed processing Dev {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:52.333 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 16/69: Ekonomi (<EMAIL>) {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:52.334 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing user 16 of 69: Ekonomi (<EMAIL>) {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:52.423 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Found 27 mail folders for Ekonomi {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:52.424 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 1 of 27: -------------------------------------------- {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:52.424 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for --------------------------------------------... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:52.504 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in -------------------------------------------- {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:52.556 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 2 of 27: Archive {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:52.556 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Archive... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:52.616 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Archive {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:52.667 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 3 of 27: Årsbokslut {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:52.667 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Årsbokslut... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:52.724 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Årsbokslut {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:52.775 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 4 of 27: Borttagna objekt {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:52.775 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Borttagna objekt... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:52.857 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Borttagna objekt {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:52.908 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 5 of 27: Conversation History {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:52.909 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Conversation History... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:52.968 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Conversation History {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:53.019 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 6 of 27: EU-bidrag {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:53.019 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for EU-bidrag... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:53.074 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in EU-bidrag {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:53.125 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 7 of 27: Fora {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:53.127 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Fora... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:53.185 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Fora {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:53.237 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 8 of 27: Försäkringskassan {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:53.238 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Försäkringskassan... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:53.293 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Försäkringskassan {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:53.345 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 9 of 27: Inkorg {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:53.345 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Inkorg... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:53.401 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Inkorg {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:53.452 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 10 of 27: Junk E-mail {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:53.453 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Junk E-mail... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:53.505 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Junk E-mail {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:53.557 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 11 of 27: Kund och partner {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:53.557 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Kund och partner... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:53.612 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Kund och partner {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:53.664 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 12 of 27: Kvitto {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:53.664 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Kvitto... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:53.720 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Kvitto {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:53.771 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 13 of 27: Leverantör {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:53.772 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Leverantör... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:53.832 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Leverantör {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:53.883 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 14 of 27: Mall {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:53.884 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Mall... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:53.974 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Mall {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:54.025 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 15 of 27: Månadsbokslut {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:54.025 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Månadsbokslut... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:54.135 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Månadsbokslut {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:54.187 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Processing folder 16 of 27: Microsoft Viva {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:54.187 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Checking message count for Microsoft Viva... {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:54.238 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: No messages found in Microsoft Viva {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:54.249 +02:00 ERR] EmailAddressExtractor.Api.Services.GraphExtractionService: Error processing user Ekonomi: A task was canceled. {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
System.Threading.Tasks.TaskCanceledException: A task was canceled.
   at EmailAddressExtractor.Api.Services.GraphExtractionService.ProcessUserEmails(User user, ExtractionRequest request, Int32 currentUserIndex, Int32 totalUsers, CancellationToken cancellationToken) in /Users/<USER>/dev/EmailAddressExtractor/EmailAddressExtractor.Api/Services/GraphExtractionService.cs:line 288
   at EmailAddressExtractor.Api.Services.GraphExtractionService.ExtractEmailsAsync(ExtractionRequest request, CancellationToken cancellationToken) in /Users/<USER>/dev/EmailAddressExtractor/EmailAddressExtractor.Api/Services/GraphExtractionService.cs:line 84
[2025-06-16 16:59:54.251 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Error processing user Ekonomi: A task was canceled. {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:54.251 +02:00 ERR] EmailAddressExtractor.Api.Services.GraphExtractionService: Error during email extraction after 35379.585ms: A task was canceled. {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
System.Threading.Tasks.TaskCanceledException: A task was canceled.
   at EmailAddressExtractor.Api.Services.GraphExtractionService.ExtractEmailsAsync(ExtractionRequest request, CancellationToken cancellationToken) in /Users/<USER>/dev/EmailAddressExtractor/EmailAddressExtractor.Api/Services/GraphExtractionService.cs:line 108
[2025-06-16 16:59:54.251 +02:00 INF] EmailAddressExtractor.Api.Services.GraphExtractionService: Error: A task was canceled. {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:54.252 +02:00 WRN] EmailAddressExtractor.Api.Controllers.ExtractionController: Email extraction was cancelled {"ActionId":"19f308b6-46d4-47ca-a070-8c6964889825","ActionName":"EmailAddressExtractor.Api.Controllers.ExtractionController.ExtractEmails (EmailAddressExtractor.Api)","RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
[2025-06-16 16:59:54.277 +02:00 INF] Microsoft.AspNetCore.Hosting.Diagnostics: Request finished HTTP/2 POST https://localhost:7012/api/extraction/extract - 499 null text/plain; charset=utf-8 35469.6462ms {"EventId":{"Id":2},"RequestId":"0HNDCSP9NQS5F:00000007","RequestPath":"/api/extraction/extract","ConnectionId":"0HNDCSP9NQS5F"}
