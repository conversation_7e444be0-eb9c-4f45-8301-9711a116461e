using Microsoft.AspNetCore.SignalR;
using EmailAddressExtractor.Api.Models;

namespace EmailAddressExtractor.Api.Hubs;

public class ProgressHub : Hub
{
    public async Task SendLogMessage(string message)
    {
        await Clients.All.SendAsync("LogMessage", message);
    }

    public async Task SendProgressUpdate(ProgressUpdate progress)
    {
        await Clients.All.SendAsync("ProgressUpdate", progress);
    }
}
