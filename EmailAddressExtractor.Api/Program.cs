using EmailAddressExtractor.Api.Hubs;
using EmailAddressExtractor.Api.Models;
using EmailAddressExtractor.Api.Services;
using Serilog;

var builder = WebApplication.CreateBuilder(args);

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(builder.Configuration)
    .CreateLogger();

builder.Host.UseSerilog();

// Add services to the container
builder.Services.AddControllers();
builder.Services.AddSignalR();

// Configure extraction settings
builder.Services.Configure<ExtractionSettings>(
    builder.Configuration.GetSection("ExtractionSettings"));

// Register services
builder.Services.AddScoped<ResilientGraphService>();
builder.Services.AddScoped<GraphExtractionService>();

// Add CORS - Fix for SignalR and API calls
builder.Services.AddCors(options =>
{
    options.AddPolicy("BlazorWasmPolicy", policy =>
    {
        policy.WithOrigins("https://localhost:7051", "http://localhost:5048")
              .AllowAnyHeader()
              .AllowAnyMethod()
              .AllowCredentials()
              .SetIsOriginAllowedToAllowWildcardSubdomains();
    });
});

// Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
builder.Services.AddOpenApi();

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
}

app.UseHttpsRedirection();

// Use CORS
app.UseCors("BlazorWasmPolicy");

// Map controllers and SignalR hub
app.MapControllers();
app.MapHub<ProgressHub>("/progressHub");

try
{
    Log.Information("Starting Email Address Extractor API");
    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "Application terminated unexpectedly");
}
finally
{
    Log.CloseAndFlush();
}
