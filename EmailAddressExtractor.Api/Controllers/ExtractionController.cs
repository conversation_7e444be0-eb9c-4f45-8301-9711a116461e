using Microsoft.AspNetCore.Mvc;
using EmailAddressExtractor.Api.Models;
using EmailAddressExtractor.Api.Services;

namespace EmailAddressExtractor.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class ExtractionController : ControllerBase
{
    private readonly GraphExtractionService _extractionService;
    private readonly ILogger<ExtractionController> _logger;

    public ExtractionController(
        GraphExtractionService extractionService,
        ILogger<ExtractionController> logger)
    {
        _extractionService = extractionService;
        _logger = logger;
    }

    [HttpPost("extract")]
    public async Task<ActionResult<List<EmailResult>>> ExtractEmails(
        [FromBody] ExtractionRequest request,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Received email extraction request with scope: {Scope}, TenantId: {TenantId}",
                request.Scope, request.M365Config.TenantId);

            // Validate the M365 configuration
            if (!request.M365Config.IsValid())
            {
                _logger.LogWarning("Invalid M365 configuration provided");
                return BadRequest(new { error = "Invalid M365 configuration. Please provide TenantId, ClientId, and ClientSecret." });
            }

            _logger.LogInformation("Starting email extraction with scope: {Scope}", request.Scope);

            var results = await _extractionService.ExtractEmailsAsync(request, cancellationToken);

            _logger.LogInformation("Email extraction completed. Found {Count} unique emails", results.Count);

            return Ok(results);
        }
        catch (OperationCanceledException)
        {
            _logger.LogWarning("Email extraction was cancelled");
            return StatusCode(499, "Request was cancelled");
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid request parameters");
            return BadRequest(new { error = ex.Message });
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogError(ex, "Authentication failed");
            return Unauthorized(new { error = "Authentication failed. Please check your M365 app credentials." });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during email extraction");
            return StatusCode(500, new { error = "An unexpected error occurred during email extraction." });
        }
    }

    [HttpGet("health")]
    public IActionResult Health()
    {
        return Ok(new { status = "healthy", timestamp = DateTime.UtcNow });
    }
}
