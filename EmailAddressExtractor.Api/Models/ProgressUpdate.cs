namespace EmailAddressExtractor.Api.Models;

public record ProgressUpdate(
    ProgressStage Stage,
    int TotalUsers,
    int CurrentUserIndex,
    string? CurrentUserName,
    int TotalFolders,
    int CurrentFolderIndex,
    string? CurrentFolderName,
    int TotalEmails,
    int ProcessedEmails,
    int TotalEmailsProcessedOverall,
    int UniqueEmailsFound
);

public enum ProgressStage
{
    Initializing,
    FetchingUsers,
    ProcessingUsers,
    ProcessingFolders,
    ProcessingEmails,
    Completed,
    Error
}
