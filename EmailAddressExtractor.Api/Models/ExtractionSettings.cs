namespace EmailAddressExtractor.Api.Models;

public class ExtractionSettings
{
    /// <summary>
    /// Base batch size for email progress updates. Will be dynamically adjusted to ensure max 100 updates per folder.
    /// </summary>
    public int EmailProgressBatchSize { get; set; } = 10;

    /// <summary>
    /// Maximum number of progress updates per folder (default: 100, meaning minimum 1% progress per update)
    /// </summary>
    public int MaxProgressUpdatesPerFolder { get; set; } = 100;

    /// <summary>
    /// Minimum batch size to prevent too frequent updates
    /// </summary>
    public int MinEmailProgressBatchSize { get; set; } = 1;

    /// <summary>
    /// Maximum batch size to ensure reasonable update frequency
    /// </summary>
    public int MaxEmailProgressBatchSize { get; set; } = 1000;

    public string DefaultInternalDomains { get; set; } = string.Empty;
    public string DefaultGenericPrefixes { get; set; } = "noreply,no-reply,support,help,contact,info,admin,marketing,sales,hr,finance";

    /// <summary>
    /// Calculate dynamic batch size based on total emails to ensure max updates per folder
    /// </summary>
    public int CalculateDynamicBatchSize(int totalEmails)
    {
        if (totalEmails <= 0) return EmailProgressBatchSize;

        // Calculate batch size to get approximately MaxProgressUpdatesPerFolder updates
        var dynamicBatchSize = Math.Max(1, totalEmails / MaxProgressUpdatesPerFolder);

        // Clamp to min/max bounds
        return Math.Max(MinEmailProgressBatchSize, Math.Min(MaxEmailProgressBatchSize, dynamicBatchSize));
    }
}
