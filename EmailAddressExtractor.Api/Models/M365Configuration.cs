namespace EmailAddressExtractor.Api.Models;

public class M365Configuration
{
    public string TenantId { get; set; } = string.Empty;
    public string ClientId { get; set; } = string.Empty;
    public string ClientSecret { get; set; } = string.Empty;
    
    public bool IsValid()
    {
        return !string.IsNullOrWhiteSpace(TenantId) &&
               !string.IsNullOrWhiteSpace(ClientId) &&
               !string.IsNullOrWhiteSpace(ClientSecret);
    }
}
