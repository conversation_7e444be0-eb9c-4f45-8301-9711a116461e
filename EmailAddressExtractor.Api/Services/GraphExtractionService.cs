using Microsoft.AspNetCore.SignalR;
using Microsoft.Extensions.Options;
using Microsoft.Graph;
using Microsoft.Graph.Models;
using EmailAddressExtractor.Api.Hubs;
using EmailAddressExtractor.Api.Models;
using System.Net;

namespace EmailAddressExtractor.Api.Services;

public class GraphExtractionService
{
    private readonly IHubContext<ProgressHub> _hubContext;
    private readonly ILogger<GraphExtractionService> _logger;
    private readonly ExtractionSettings _extractionSettings;
    private readonly ResilientGraphService _resilientGraphService;
    private readonly Dictionary<string, EmailResult> _emailResults = new();
    private HashSet<string> _companyDomainSet = new();
    private HashSet<string> _genericPrefixSet = new();
    private int _totalEmailsProcessedOverall = 0;

    public GraphExtractionService(
        IHubContext<ProgressHub> hubContext,
        ILogger<GraphExtractionService> logger,
        IOptions<ExtractionSettings> extractionSettings,
        ResilientGraphService resilientGraphService)
    {
        _hubContext = hubContext;
        _logger = logger;
        _extractionSettings = extractionSettings.Value;
        _resilientGraphService = resilientGraphService;
    }

    public async Task<List<EmailResult>> ExtractEmailsAsync(ExtractionRequest request, CancellationToken cancellationToken = default)
    {
        var operationStartTime = DateTime.UtcNow;
        try
        {
            _logger.LogInformation("Starting email extraction process with scope: {Scope} for tenant: {TenantId}",
                request.Scope, request.M365Config.TenantId);
            await SendLogMessage("Starting email extraction process...");
            await SendProgressUpdate(new ProgressUpdate(
                ProgressStage.Initializing, 0, 0, null, 0, 0, null, 0, 0, 0, 0));

            // Validate M365 configuration
            if (!request.M365Config.IsValid())
            {
                var error = "Invalid M365 configuration provided";
                _logger.LogError(error);
                throw new ArgumentException(error);
            }

            _logger.LogInformation("M365 configuration validated for tenant: {TenantId}", request.M365Config.TenantId);

            // Initialize resilient Graph client with cancellation support
            await _resilientGraphService.InitializeAsync(request.M365Config, cancellationToken);

            // Prepare filtering sets
            PrepareFilteringSets(request);

            // Get users to process
            await SendProgressUpdate(new ProgressUpdate(
                ProgressStage.FetchingUsers, 0, 0, null, 0, 0, null, 0, 0, 0, 0));

            var users = await GetUsersToProcess(request, cancellationToken);
            await SendLogMessage($"Found {users.Count} users to process");

            await SendProgressUpdate(new ProgressUpdate(
                ProgressStage.ProcessingUsers, users.Count, 0, null, 0, 0, null, 0, 0, 0, 0));

            // Process each user with enhanced error handling
            for (int i = 0; i < users.Count && !cancellationToken.IsCancellationRequested; i++)
            {
                var user = users[i];
                var userStartTime = DateTime.UtcNow;

                _logger.LogInformation("Processing user {UserIndex}/{TotalUsers}: {DisplayName} ({UserPrincipalName})",
                    i + 1, users.Count, user.DisplayName, user.UserPrincipalName);
                await SendLogMessage($"Processing user {i + 1} of {users.Count}: {user.DisplayName} ({user.UserPrincipalName})");

                try
                {
                    await ProcessUserEmails(user, request, i + 1, users.Count, cancellationToken);

                    var userDuration = DateTime.UtcNow - userStartTime;
                    _logger.LogDebug("Completed processing user {DisplayName} in {Duration}ms",
                        user.DisplayName, userDuration.TotalMilliseconds);
                }
                catch (ServiceException ex) when (ex.ResponseStatusCode == (int)HttpStatusCode.TooManyRequests)
                {
                    _logger.LogWarning("Rate limited while processing user {DisplayName}. Status: {StatusCode}, Message: {Message}",
                        user.DisplayName, ex.ResponseStatusCode, ex.Message);
                    await SendLogMessage($"Rate limited for user {user.DisplayName}, retrying with backoff...");
                    // The resilient service will handle retries automatically
                    throw;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing user {DisplayName}: {Message}", user.DisplayName, ex.Message);
                    await SendLogMessage($"Error processing user {user.DisplayName}: {ex.Message}");
                    // Continue with next user instead of failing entire operation
                }

                // Adaptive delay based on processing time
                var processingTime = DateTime.UtcNow - userStartTime;
                var delay = processingTime.TotalMilliseconds < 1000 ? 200 : 50; // Longer delay if processing was fast
                await Task.Delay(delay, cancellationToken);
            }

            var totalDuration = DateTime.UtcNow - operationStartTime;
            if (!cancellationToken.IsCancellationRequested)
            {
                await SendProgressUpdate(new ProgressUpdate(
                    ProgressStage.Completed, users.Count, users.Count, null, 0, 0, null, 0, 0, _totalEmailsProcessedOverall, _emailResults.Count));

                _logger.LogInformation("Extraction completed successfully! Found {UniqueEmails} unique external email addresses from {TotalEmails} total emails processed in {Duration}ms",
                    _emailResults.Count, _totalEmailsProcessedOverall, totalDuration.TotalMilliseconds);
                await SendLogMessage($"Extraction completed! Found {_emailResults.Count} unique external email addresses from {_totalEmailsProcessedOverall} total emails processed.");
            }
            else
            {
                _logger.LogWarning("Extraction was cancelled after {Duration}ms", totalDuration.TotalMilliseconds);
                await SendLogMessage("Extraction was cancelled.");
            }

            return _emailResults.Values.ToList();
        }
        catch (Exception ex)
        {
            var totalDuration = DateTime.UtcNow - operationStartTime;
            _logger.LogError(ex, "Error during email extraction after {Duration}ms: {Message}", totalDuration.TotalMilliseconds, ex.Message);
            await SendLogMessage($"Error: {ex.Message}");
            await SendProgressUpdate(new ProgressUpdate(
                ProgressStage.Error, 0, 0, null, 0, 0, null, 0, 0, _totalEmailsProcessedOverall, _emailResults.Count));
            throw;
        }
    }



    private void PrepareFilteringSets(ExtractionRequest request)
    {
        _companyDomainSet = request.InternalDomains
            .Split(',', StringSplitOptions.RemoveEmptyEntries)
            .Select(d => d.Trim().ToLowerInvariant())
            .ToHashSet();
            
        _genericPrefixSet = request.GenericPrefixes
            .Split(',', StringSplitOptions.RemoveEmptyEntries)
            .Select(p => p.Trim().ToLowerInvariant())
            .ToHashSet();
            
        _logger.LogInformation("Configured {CompanyDomains} company domains and {GenericPrefixes} generic prefixes for filtering", 
            _companyDomainSet.Count, _genericPrefixSet.Count);
    }

    private async Task<List<User>> GetUsersToProcess(ExtractionRequest request, CancellationToken cancellationToken)
    {
        var fetchStartTime = DateTime.UtcNow;
        _logger.LogInformation("Fetching users to process for scope: {Scope}", request.Scope);
        await SendLogMessage("Fetching users to process...");
        await SendProgressUpdate(new ProgressUpdate(
            ProgressStage.FetchingUsers, 0, 0, null, 0, 0, null, 0, 0, 0, 0));

        var users = new List<User>();

        try
        {
            switch (request.Scope)
            {
                case ScopeType.All:
                    _logger.LogInformation("Fetching all users from tenant");
                    await SendLogMessage("Fetching all users from tenant...");

                    var allUsers = await _resilientGraphService.GetUsersAsync(cancellationToken);

                    if (allUsers?.Value != null)
                    {
                        users.AddRange(allUsers.Value);
                        _logger.LogInformation("Fetched {UserCount} users from tenant", allUsers.Value.Count);

                        // Note: For now, we'll rely on the initial page size (999) to get most users
                        // Complex pagination can be added later if needed for very large tenants
                    }
                    break;
                    
                case ScopeType.Group:
                    _logger.LogInformation("Fetching users from group: {GroupId}", request.ScopeIdentifier);
                    await SendLogMessage($"Fetching users from group: {request.ScopeIdentifier}");

                    var groupMembers = await _resilientGraphService.GetGroupMembersAsync(request.ScopeIdentifier!, cancellationToken);

                    if (groupMembers?.Value != null)
                    {
                        users.AddRange(groupMembers.Value.OfType<User>());
                        _logger.LogInformation("Fetched {UserCount} users from group {GroupId}", groupMembers.Value.Count, request.ScopeIdentifier);

                        // Handle pagination for group members - simplified
                        // Note: For now, we'll rely on the initial page size to get most members
                        // Complex pagination can be added later if needed
                    }
                    break;
                    
                case ScopeType.SpecificUsers:
                    var userPrincipals = request.ScopeIdentifier?
                        .Split(',', StringSplitOptions.RemoveEmptyEntries)
                        .Select(u => u.Trim())
                        .Where(u => !string.IsNullOrEmpty(u))
                        .ToList() ?? new List<string>();

                    _logger.LogInformation("Fetching {UserCount} specific users", userPrincipals.Count);
                    await SendLogMessage($"Fetching {userPrincipals.Count} specific users...");

                    foreach (var upn in userPrincipals)
                    {
                        try
                        {
                            var user = await _resilientGraphService.GetUserAsync(upn, cancellationToken);

                            if (user != null)
                            {
                                users.Add(user);
                                _logger.LogDebug("Successfully fetched user: {UserPrincipalName}", upn);
                            }
                        }
                        catch (ServiceException ex) when (ex.ResponseStatusCode == 404)
                        {
                            _logger.LogWarning("User not found: {UserPrincipalName}", upn);
                            await SendLogMessage($"Could not find user: {upn}");
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Error fetching user {UserPrincipalName}: {Message}", upn, ex.Message);
                            await SendLogMessage($"Error fetching user: {upn} - {ex.Message}");
                        }
                    }
                    break;
            }
        }
        catch (ServiceException ex)
        {
            _logger.LogError(ex, "Graph API error while fetching users. Status: {StatusCode}, Message: {Message}",
                ex.ResponseStatusCode, ex.Message);
            throw new Exception($"Failed to get users from Microsoft Graph: {ex.Message}", ex);
        }
        catch (Exception ex)
        {
            var duration = DateTime.UtcNow - fetchStartTime;
            _logger.LogError(ex, "Failed to get users after {Duration}ms: {Message}", duration.TotalMilliseconds, ex.Message);
            throw new Exception($"Failed to get users: {ex.Message}", ex);
        }

        var finalDuration = DateTime.UtcNow - fetchStartTime;
        _logger.LogInformation("Successfully fetched {UserCount} users in {Duration}ms", users.Count, finalDuration.TotalMilliseconds);
        return users.Where(u => u != null).ToList();
    }

    private async Task ProcessUserEmails(User user, ExtractionRequest request, int currentUserIndex, int totalUsers, CancellationToken cancellationToken)
    {
        try
        {
            // Get all mail folders for the user - this will naturally detect mailbox accessibility issues
            var folders = await GetMailFolders(user.Id ?? string.Empty, cancellationToken);
            var validFolders = folders.Where(f => !IsSystemFolder(f.DisplayName ?? string.Empty)).ToList();

            if (validFolders.Count == 0)
            {
                await SendLogMessage($"No accessible mail folders found for {user.DisplayName}");
                return;
            }

            await SendLogMessage($"Found {validFolders.Count} mail folders for {user.DisplayName}");

            await SendProgressUpdate(new ProgressUpdate(
                ProgressStage.ProcessingFolders, totalUsers, currentUserIndex, user.DisplayName,
                validFolders.Count, 0, null, 0, 0, _totalEmailsProcessedOverall, _emailResults.Count));

            for (int i = 0; i < validFolders.Count && !cancellationToken.IsCancellationRequested; i++)
            {
                var folder = validFolders[i];

                await SendLogMessage($"Processing folder {i + 1} of {validFolders.Count}: {folder.DisplayName}");

                await ProcessFolderEmails(user, folder, request, currentUserIndex, totalUsers, i + 1, validFolders.Count, cancellationToken);

                // Small delay to prevent overwhelming the API
                await Task.Delay(50, cancellationToken);
            }

            await SendLogMessage($"Completed processing {user.DisplayName}");
        }
        catch (OperationCanceledException)
        {
            throw;
        }
        catch (ServiceException ex) when (
            ex.Message.Contains("mailbox is either inactive, soft-deleted, or is hosted on-premise", StringComparison.OrdinalIgnoreCase) ||
            ex.Message.Contains("does not have a mailbox", StringComparison.OrdinalIgnoreCase) ||
            ex.Message.Contains("on-premise", StringComparison.OrdinalIgnoreCase))
        {
            _logger.LogInformation("Skipping user {DisplayName} ({UserPrincipalName}) - mailbox not accessible: {ErrorMessage}",
                user.DisplayName, user.UserPrincipalName, ex.Message);
            await SendLogMessage($"Skipping {user.DisplayName} - mailbox not accessible (on-premise or inactive)");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing emails for user {DisplayName} ({UserPrincipalName}): {ErrorMessage}",
                user.DisplayName, user.UserPrincipalName, ex.Message);
            await SendLogMessage($"Error processing {user.DisplayName}: {ex.Message}");
        }
    }

    private async Task<List<MailFolder>> GetMailFolders(string userId, CancellationToken cancellationToken)
    {
        var folders = new List<MailFolder>();
        var startTime = DateTime.UtcNow;

        try
        {
            _logger.LogDebug("Fetching mail folders for user: {UserId}", userId);

            var folderCollection = await _resilientGraphService.GetMailFoldersAsync(userId, cancellationToken);

            if (folderCollection?.Value != null)
            {
                folders.AddRange(folderCollection.Value);

                // Handle pagination - simplified approach
                // Note: Most users don't have more than 999 folders, so this should be sufficient

                var duration = DateTime.UtcNow - startTime;
                _logger.LogDebug("Fetched {FolderCount} mail folders for user {UserId} in {Duration}ms",
                    folders.Count, userId, duration.TotalMilliseconds);
            }
        }
        catch (ServiceException ex) when (
            ex.Message.Contains("mailbox is either inactive, soft-deleted, or is hosted on-premise", StringComparison.OrdinalIgnoreCase) ||
            ex.Message.Contains("does not have a mailbox", StringComparison.OrdinalIgnoreCase) ||
            ex.Message.Contains("on-premise", StringComparison.OrdinalIgnoreCase))
        {
            _logger.LogInformation("User {UserId} does not have accessible Exchange Online mailbox: {ErrorMessage}",
                userId, ex.Message);
            // Return empty list for users without accessible mailboxes
        }
        catch (ServiceException ex)
        {
            _logger.LogError(ex, "Graph API error getting mail folders for user {UserId}. Status: {StatusCode}",
                userId, ex.ResponseStatusCode);
            await SendLogMessage($"Error getting mail folders for user {userId}: {ex.Message}");
        }
        catch (Exception ex)
        {
            var duration = DateTime.UtcNow - startTime;
            _logger.LogError(ex, "Error getting mail folders for user {UserId} after {Duration}ms", userId, duration.TotalMilliseconds);
            await SendLogMessage($"Error getting mail folders for user {userId}: {ex.Message}");
        }

        return folders;
    }

    private bool IsSystemFolder(string folderName)
    {
        var systemFolders = new[] { "Deleted Items", "Junk Email", "Drafts", "Outbox" };
        return systemFolders.Contains(folderName, StringComparer.OrdinalIgnoreCase);
    }

    private async Task ProcessFolderEmails(User user, MailFolder folder, ExtractionRequest request,
        int currentUserIndex, int totalUsers, int currentFolderIndex, int totalFolders, CancellationToken cancellationToken)
    {
        var folderStartTime = DateTime.UtcNow;
        try
        {
            // Get all messages from the folder with automatic pagination
            var allMessages = await _resilientGraphService.GetAllMessagesAsync(user.Id!, folder.Id!, request.StartDate, request.EndDate, cancellationToken);

            // Now we have the actual count of messages we'll process
            var messageCount = allMessages.Count;

            // Calculate dynamic batch size for this folder based on actual count
            var dynamicBatchSize = _extractionSettings.CalculateDynamicBatchSize(messageCount);
            _logger.LogDebug("Processing folder {FolderName} with {MessageCount} messages using batch size {BatchSize}",
                folder.DisplayName, messageCount, dynamicBatchSize);

            await SendProgressUpdate(new ProgressUpdate(
                ProgressStage.ProcessingEmails, totalUsers, currentUserIndex, user.DisplayName,
                totalFolders, currentFolderIndex, folder.DisplayName, messageCount, 0, _totalEmailsProcessedOverall, _emailResults.Count));

            int processedInFolder = 0;

            _logger.LogDebug("Processing {MessageCount} messages for folder {FolderName}",
                allMessages.Count, folder.DisplayName);

            foreach (var message in allMessages)
            {
                if (cancellationToken.IsCancellationRequested) return;

                ProcessEmailAddresses(message, $"{user.DisplayName} - {folder.DisplayName}");
                processedInFolder++;
                _totalEmailsProcessedOverall++;

                // Update progress based on dynamic batch size
                if (processedInFolder % dynamicBatchSize == 0)
                {
                    await SendProgressUpdate(new ProgressUpdate(
                        ProgressStage.ProcessingEmails, totalUsers, currentUserIndex, user.DisplayName,
                        totalFolders, currentFolderIndex, folder.DisplayName, messageCount, processedInFolder,
                        _totalEmailsProcessedOverall, _emailResults.Count));

                    // Adaptive delay based on processing speed
                    await Task.Delay(5, cancellationToken);
                }
            }

            // Final progress update for this folder
            await SendProgressUpdate(new ProgressUpdate(
                ProgressStage.ProcessingEmails, totalUsers, currentUserIndex, user.DisplayName,
                totalFolders, currentFolderIndex, folder.DisplayName, messageCount, processedInFolder,
                _totalEmailsProcessedOverall, _emailResults.Count));

            var folderDuration = DateTime.UtcNow - folderStartTime;
            if (processedInFolder > 0)
            {
                _logger.LogInformation("Processed {ProcessedCount} emails from folder {FolderName} in {Duration}ms",
                    processedInFolder, folder.DisplayName, folderDuration.TotalMilliseconds);
                await SendLogMessage($"Processed {processedInFolder} emails from {folder.DisplayName}");
            }
        }
        catch (OperationCanceledException)
        {
            _logger.LogInformation("Processing cancelled for folder {FolderName}", folder.DisplayName);
            throw;
        }
        catch (ServiceException ex) when (ex.ResponseStatusCode == (int)HttpStatusCode.TooManyRequests)
        {
            _logger.LogWarning("Rate limited while processing folder {FolderName}. Status: {StatusCode}, Message: {Message}",
                folder.DisplayName, ex.ResponseStatusCode, ex.Message);
            await SendLogMessage($"Rate limited for folder {folder.DisplayName}, retrying with backoff...");
            throw; // Let the resilient service handle retries
        }
        catch (ServiceException ex)
        {
            _logger.LogError(ex, "Graph API error processing folder {FolderName}. Status: {StatusCode}",
                folder.DisplayName, ex.ResponseStatusCode);
            await SendLogMessage($"Error processing folder {folder.DisplayName}: {ex.Message}");
        }
        catch (Exception ex)
        {
            var folderDuration = DateTime.UtcNow - folderStartTime;
            _logger.LogError(ex, "Error processing folder {FolderName} after {Duration}ms: {Message}",
                folder.DisplayName, folderDuration.TotalMilliseconds, ex.Message);
            await SendLogMessage($"Error processing folder {folder.DisplayName}: {ex.Message}");
        }
    }

    private async Task<int> GetFolderMessageCount(string userId, string folderId, ExtractionRequest request, CancellationToken cancellationToken)
    {
        try
        {
            // Use the dedicated method in ResilientGraphService to get the accurate count
            var count = await _resilientGraphService.GetMessageCountAsync(userId, folderId, request.StartDate, request.EndDate, cancellationToken);

            _logger.LogDebug("Folder {FolderId} contains {MessageCount} messages matching filter", folderId, count);
            return count;
        }
        catch (ServiceException ex) when (
            ex.Message.Contains("mailbox is either inactive, soft-deleted, or is hosted on-premise", StringComparison.OrdinalIgnoreCase) ||
            ex.Message.Contains("does not have a mailbox", StringComparison.OrdinalIgnoreCase) ||
            ex.Message.Contains("on-premise", StringComparison.OrdinalIgnoreCase))
        {
            _logger.LogInformation("Cannot get message count for folder {FolderId} - mailbox not accessible: {ErrorMessage}",
                folderId, ex.Message);
            return 0; // Return 0 for inaccessible mailboxes
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Could not get message count for folder {FolderId}: {Message}", folderId, ex.Message);
            return 0; // Return 0 if we can't get the count
        }
    }

    private void ProcessEmailAddresses(Message message, string source)
    {
        var addresses = new List<Recipient>();
        
        // Collect all email addresses from the message
        if (message.From?.EmailAddress != null)
            addresses.Add(message.From);
        
        if (message.ToRecipients != null)
            addresses.AddRange(message.ToRecipients);
        
        if (message.CcRecipients != null)
            addresses.AddRange(message.CcRecipients);
        
        if (message.BccRecipients != null)
            addresses.AddRange(message.BccRecipients);
        
        foreach (var recipient in addresses)
        {
            if (recipient?.EmailAddress?.Address == null) continue;
            
            var email = recipient.EmailAddress.Address.ToLowerInvariant();
            var displayName = recipient.EmailAddress.Name;
            
            // Apply filtering
            if (ShouldExcludeEmail(email)) continue;
            
            // Add or update the email result
            if (_emailResults.ContainsKey(email))
            {
                var existing = _emailResults[email];
                _emailResults[email] = existing with { MentionCount = existing.MentionCount + 1 };
            }
            else
            {
                _emailResults[email] = new EmailResult(
                    Id: Guid.NewGuid().ToString(),
                    EmailAddress: email,
                    DisplayName: displayName,
                    MentionCount: 1,
                    CollectedFrom: source
                );
            }
        }
    }

    private bool ShouldExcludeEmail(string email)
    {
        if (string.IsNullOrWhiteSpace(email)) return true;
        
        // Extract domain
        var atIndex = email.LastIndexOf('@');
        if (atIndex <= 0 || atIndex >= email.Length - 1) return true;
        
        var domain = email.Substring(atIndex + 1).ToLowerInvariant();
        var localPart = email.Substring(0, atIndex).ToLowerInvariant();
        
        // Exclude company domains
        if (_companyDomainSet.Contains(domain)) return true;
        
        // Exclude generic prefixes
        if (_genericPrefixSet.Any(prefix => localPart.StartsWith(prefix))) return true;
        
        return false;
    }

    private async Task SendLogMessage(string message)
    {
        _logger.LogInformation(message);
        await _hubContext.Clients.All.SendAsync("LogMessage", message);
    }

    private async Task SendProgressUpdate(ProgressUpdate progress)
    {
        await _hubContext.Clients.All.SendAsync("ProgressUpdate", progress);
    }
}
