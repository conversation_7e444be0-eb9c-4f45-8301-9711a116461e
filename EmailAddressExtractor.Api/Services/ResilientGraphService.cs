using Microsoft.Graph;
using Microsoft.Graph.Models;
using Microsoft.Identity.Client;
using Polly;
using System.Net;
using EmailAddressExtractor.Api.Models;

namespace EmailAddressExtractor.Api.Services;

public class ResilientGraphService
{
    private readonly ILogger<ResilientGraphService> _logger;
    private GraphServiceClient? _graphClient;
    private readonly IAsyncPolicy _retryPolicy;

    public ResilientGraphService(ILogger<ResilientGraphService> logger)
    {
        _logger = logger;

        // Create a simple retry policy using Polly - only retry on transient errors
        _retryPolicy = Policy
            .Handle<HttpRequestException>()
            .Or<TaskCanceledException>()
            .Or<ServiceException>(ex =>
                ex.ResponseStatusCode == (int)HttpStatusCode.TooManyRequests ||
                ex.ResponseStatusCode == (int)HttpStatusCode.ServiceUnavailable ||
                ex.ResponseStatusCode == (int)HttpStatusCode.InternalServerError ||
                ex.ResponseStatusCode == (int)HttpStatusCode.RequestTimeout ||
                ex.ResponseStatusCode == (int)HttpStatusCode.BadGateway ||
                ex.ResponseStatusCode == (int)HttpStatusCode.GatewayTimeout)
            .WaitAndRetryAsync(
                retryCount: 5,
                sleepDurationProvider: retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)) + TimeSpan.FromMilliseconds(Random.Shared.Next(0, 1000)),
                onRetry: (outcome, timespan, retryCount, context) =>
                {
                    _logger.LogWarning("Graph API call failed (attempt {RetryCount}), retrying in {Delay}ms. Exception: {ExceptionMessage}",
                        retryCount, timespan.TotalMilliseconds, outcome.Message);
                });
    }

    public async Task InitializeAsync(M365Configuration config, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Initializing resilient Graph client for tenant: {TenantId}", config.TenantId);

            var app = ConfidentialClientApplicationBuilder
                .Create(config.ClientId)
                .WithClientSecret(config.ClientSecret)
                .WithAuthority(new Uri($"https://login.microsoftonline.com/{config.TenantId}"))
                .Build();

            // Get access token with resilience
            var scopes = new[] { "https://graph.microsoft.com/.default" };
            _logger.LogDebug("Acquiring token with resilience policies");

            var result = await _retryPolicy.ExecuteAsync(async () =>
            {
                return await app.AcquireTokenForClient(scopes).ExecuteAsync(cancellationToken);
            });

            _logger.LogDebug("Successfully acquired access token");

            // Create HTTP client with resilience policies
            var httpClient = new HttpClient();
            httpClient.DefaultRequestHeaders.Authorization =
                new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", result.AccessToken);

            // Add timeout for individual requests
            httpClient.Timeout = TimeSpan.FromMinutes(5);

            _graphClient = new GraphServiceClient(httpClient);

            // Test the connection with resilience
            _logger.LogDebug("Testing Graph connection");
            await _retryPolicy.ExecuteAsync(async () =>
            {
                var org = await _graphClient.Organization.GetAsync(cancellationToken: cancellationToken);
                return org;
            });

            _logger.LogInformation("Successfully initialized resilient Graph client for tenant: {TenantId}", config.TenantId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize resilient Graph client for tenant: {TenantId}", config.TenantId);
            throw new Exception($"Failed to initialize Graph client: {ex.Message}", ex);
        }
    }

    public async Task<UserCollectionResponse?> GetUsersAsync(CancellationToken cancellationToken = default)
    {
        if (_graphClient == null) throw new InvalidOperationException("Graph client not initialized");

        _logger.LogDebug("Fetching users with resilience policies");
        var startTime = DateTime.UtcNow;

        try
        {
            var result = await _retryPolicy.ExecuteAsync(async () =>
            {
                return await _graphClient.Users.GetAsync(requestConfiguration => {
                    requestConfiguration.QueryParameters.Select = new[] { "id", "userPrincipalName", "displayName" };
                    requestConfiguration.QueryParameters.Top = 999;
                }, cancellationToken);
            });

            var duration = DateTime.UtcNow - startTime;
            _logger.LogDebug("Successfully fetched users in {Duration}ms", duration.TotalMilliseconds);
            return result;
        }
        catch (Exception ex)
        {
            var duration = DateTime.UtcNow - startTime;
            _logger.LogError(ex, "Failed to fetch users after {Duration}ms", duration.TotalMilliseconds);
            throw;
        }
    }

    public async Task<User?> GetUserAsync(string userPrincipalName, CancellationToken cancellationToken = default)
    {
        if (_graphClient == null) throw new InvalidOperationException("Graph client not initialized");

        _logger.LogDebug("Fetching user {UserPrincipalName} with resilience policies", userPrincipalName);
        var startTime = DateTime.UtcNow;

        try
        {
            var result = await _retryPolicy.ExecuteAsync(async () =>
            {
                return await _graphClient.Users[userPrincipalName].GetAsync(requestConfiguration => {
                    requestConfiguration.QueryParameters.Select = new[] { "id", "userPrincipalName", "displayName" };
                }, cancellationToken);
            });

            var duration = DateTime.UtcNow - startTime;
            _logger.LogDebug("Successfully fetched user {UserPrincipalName} in {Duration}ms", userPrincipalName, duration.TotalMilliseconds);
            return result;
        }
        catch (Exception ex)
        {
            var duration = DateTime.UtcNow - startTime;
            _logger.LogError(ex, "Failed to fetch user {UserPrincipalName} after {Duration}ms", userPrincipalName, duration.TotalMilliseconds);
            throw;
        }
    }

    public async Task<DirectoryObjectCollectionResponse?> GetGroupMembersAsync(string groupId, CancellationToken cancellationToken = default)
    {
        if (_graphClient == null) throw new InvalidOperationException("Graph client not initialized");

        _logger.LogDebug("Fetching group members for {GroupId} with resilience policies", groupId);
        var startTime = DateTime.UtcNow;

        try
        {
            var result = await _retryPolicy.ExecuteAsync(async () =>
            {
                return await _graphClient.Groups[groupId].Members.GetAsync(requestConfiguration => {
                    requestConfiguration.QueryParameters.Select = new[] { "id", "userPrincipalName", "displayName" };
                    requestConfiguration.QueryParameters.Top = 999;
                }, cancellationToken);
            });

            var duration = DateTime.UtcNow - startTime;
            _logger.LogDebug("Successfully fetched group members for {GroupId} in {Duration}ms", groupId, duration.TotalMilliseconds);
            return result;
        }
        catch (Exception ex)
        {
            var duration = DateTime.UtcNow - startTime;
            _logger.LogError(ex, "Failed to fetch group members for {GroupId} after {Duration}ms", groupId, duration.TotalMilliseconds);
            throw;
        }
    }

    public async Task<MailFolderCollectionResponse?> GetMailFoldersAsync(string userId, CancellationToken cancellationToken = default)
    {
        if (_graphClient == null) throw new InvalidOperationException("Graph client not initialized");

        _logger.LogDebug("Fetching mail folders for user {UserId} with resilience policies", userId);
        var startTime = DateTime.UtcNow;

        try
        {
            var result = await _retryPolicy.ExecuteAsync(async () =>
            {
                return await _graphClient.Users[userId].MailFolders.GetAsync(requestConfiguration => {
                    requestConfiguration.QueryParameters.Select = new[] { "id", "displayName", "totalItemCount" };
                    requestConfiguration.QueryParameters.Top = 999;
                }, cancellationToken);
            });

            var duration = DateTime.UtcNow - startTime;
            _logger.LogDebug("Successfully fetched mail folders for user {UserId} in {Duration}ms", userId, duration.TotalMilliseconds);
            return result;
        }
        catch (ServiceException ex) when (
            ex.Message.Contains("mailbox is either inactive, soft-deleted, or is hosted on-premise", StringComparison.OrdinalIgnoreCase) ||
            ex.Message.Contains("does not have a mailbox", StringComparison.OrdinalIgnoreCase) ||
            ex.Message.Contains("on-premise", StringComparison.OrdinalIgnoreCase))
        {
            var duration = DateTime.UtcNow - startTime;
            _logger.LogInformation("Cannot fetch mail folders for user {UserId} - mailbox not accessible: {ErrorMessage}",
                userId, ex.Message);
            throw; // Re-throw so calling code can handle appropriately
        }
        catch (Exception ex)
        {
            var duration = DateTime.UtcNow - startTime;
            _logger.LogError(ex, "Failed to fetch mail folders for user {UserId} after {Duration}ms", userId, duration.TotalMilliseconds);
            throw;
        }
    }

    public async Task<MessageCollectionResponse?> GetMessagesAsync(string userId, string folderId, DateTime? startDate = null, DateTime? endDate = null, CancellationToken cancellationToken = default)
    {
        if (_graphClient == null) throw new InvalidOperationException("Graph client not initialized");

        _logger.LogDebug("Fetching messages for user {UserId}, folder {FolderId} with resilience policies", userId, folderId);
        var startTime = DateTime.UtcNow;

        try
        {
            var result = await _retryPolicy.ExecuteAsync(async () =>
            {
                return await _graphClient.Users[userId].MailFolders[folderId].Messages.GetAsync(requestConfiguration => {
                    requestConfiguration.QueryParameters.Select = new[] { "from", "toRecipients", "ccRecipients", "bccRecipients", "receivedDateTime" };
                    requestConfiguration.QueryParameters.Top = 50;

                    // Apply date filtering if specified
                    if (startDate.HasValue || endDate.HasValue)
                    {
                        var filter = new List<string>();
                        if (startDate.HasValue)
                            filter.Add($"receivedDateTime ge {startDate.Value:yyyy-MM-ddTHH:mm:ssZ}");
                        if (endDate.HasValue)
                            filter.Add($"receivedDateTime le {endDate.Value:yyyy-MM-ddTHH:mm:ssZ}");

                        if (filter.Any())
                            requestConfiguration.QueryParameters.Filter = string.Join(" and ", filter);
                    }
                }, cancellationToken);
            });

            var duration = DateTime.UtcNow - startTime;
            _logger.LogDebug("Successfully fetched messages for user {UserId}, folder {FolderId} in {Duration}ms", userId, folderId, duration.TotalMilliseconds);
            return result;
        }
        catch (ServiceException ex) when (
            ex.Message.Contains("mailbox is either inactive, soft-deleted, or is hosted on-premise", StringComparison.OrdinalIgnoreCase) ||
            ex.Message.Contains("does not have a mailbox", StringComparison.OrdinalIgnoreCase) ||
            ex.Message.Contains("on-premise", StringComparison.OrdinalIgnoreCase))
        {
            var duration = DateTime.UtcNow - startTime;
            _logger.LogInformation("Cannot fetch messages for user {UserId}, folder {FolderId} - mailbox not accessible: {ErrorMessage}",
                userId, folderId, ex.Message);
            throw; // Re-throw so calling code can handle appropriately
        }
        catch (Exception ex)
        {
            var duration = DateTime.UtcNow - startTime;
            _logger.LogError(ex, "Failed to fetch messages for user {UserId}, folder {FolderId} after {Duration}ms", userId, folderId, duration.TotalMilliseconds);
            throw;
        }
    }

    public async Task<T> ExecuteWithResilienceAsync<T>(Func<GraphServiceClient, Task<T>> operation, CancellationToken cancellationToken = default)
    {
        if (_graphClient == null) throw new InvalidOperationException("Graph client not initialized");

        return await _retryPolicy.ExecuteAsync(async () =>
        {
            return await operation(_graphClient);
        });
    }

    /// <summary>
    /// Gets all messages from a folder with automatic pagination
    /// </summary>
    public async Task<List<Message>> GetAllMessagesAsync(string userId, string folderId, DateTime? startDate = null, DateTime? endDate = null, CancellationToken cancellationToken = default)
    {
        if (_graphClient == null) throw new InvalidOperationException("Graph client not initialized");

        var allMessages = new List<Message>();
        var startTime = DateTime.UtcNow;
        var pageCount = 0;

        try
        {
            _logger.LogDebug("Fetching all messages for user {UserId}, folder {FolderId} with pagination", userId, folderId);

            var messages = await GetMessagesAsync(userId, folderId, startDate, endDate, cancellationToken);

            while (messages?.Value != null && !cancellationToken.IsCancellationRequested)
            {
                pageCount++;
                allMessages.AddRange(messages.Value);

                _logger.LogDebug("Fetched page {PageCount} with {MessageCount} messages for folder {FolderId}. Total so far: {TotalCount}",
                    pageCount, messages.Value.Count, folderId, allMessages.Count);

                // Check if there's a next page
                if (!string.IsNullOrEmpty(messages.OdataNextLink))
                {
                    // For pagination, we'll use a simple approach: extract the $skip parameter and make a new request
                    var uri = new Uri(messages.OdataNextLink);
                    var skipValue = ExtractSkipValueFromUrl(uri.Query);

                    if (skipValue.HasValue)
                    {
                        // Make a new request with the skip parameter
                        messages = await _retryPolicy.ExecuteAsync(async () =>
                        {
                            return await _graphClient.Users[userId].MailFolders[folderId].Messages.GetAsync(requestConfiguration => {
                                requestConfiguration.QueryParameters.Select = new[] { "from", "toRecipients", "ccRecipients", "bccRecipients", "receivedDateTime" };
                                requestConfiguration.QueryParameters.Top = 50;
                                requestConfiguration.QueryParameters.Skip = skipValue.Value;

                                // Apply date filtering if specified
                                if (startDate.HasValue || endDate.HasValue)
                                {
                                    var filter = new List<string>();
                                    if (startDate.HasValue)
                                        filter.Add($"receivedDateTime ge {startDate.Value:yyyy-MM-ddTHH:mm:ssZ}");
                                    if (endDate.HasValue)
                                        filter.Add($"receivedDateTime le {endDate.Value:yyyy-MM-ddTHH:mm:ssZ}");

                                    if (filter.Any())
                                        requestConfiguration.QueryParameters.Filter = string.Join(" and ", filter);
                                }
                            }, cancellationToken);
                        });
                    }
                    else
                    {
                        // If we can't parse the skip value, stop pagination
                        _logger.LogWarning("Unable to parse skip value from next link: {NextLink}", messages.OdataNextLink);
                        break;
                    }
                }
                else
                {
                    // No more pages
                    break;
                }
            }

            var duration = DateTime.UtcNow - startTime;
            _logger.LogInformation("Successfully fetched {TotalMessages} messages across {PageCount} pages for folder {FolderId} in {Duration}ms",
                allMessages.Count, pageCount, folderId, duration.TotalMilliseconds);

            return allMessages;
        }
        catch (Exception ex)
        {
            var duration = DateTime.UtcNow - startTime;
            _logger.LogError(ex, "Failed to fetch all messages for folder {FolderId} after {Duration}ms. Retrieved {MessageCount} messages across {PageCount} pages",
                folderId, duration.TotalMilliseconds, allMessages.Count, pageCount);
            throw;
        }
    }

    /// <summary>
    /// Safely checks if a user has an accessible Exchange Online mailbox by trying to get mail folders
    /// </summary>
    public async Task<bool> HasAccessibleMailboxAsync(string userId, CancellationToken cancellationToken = default)
    {
        if (_graphClient == null) throw new InvalidOperationException("Graph client not initialized");

        try
        {
            _logger.LogDebug("Checking mailbox accessibility for user {UserId}", userId);

            // Try to get the user's mail folders - this is the most reliable way to check mailbox access
            // We'll just get the first page with minimal data
            await _retryPolicy.ExecuteAsync(async () =>
            {
                return await _graphClient.Users[userId].MailFolders.GetAsync(requestConfiguration => {
                    requestConfiguration.QueryParameters.Select = new[] { "id" };
                    requestConfiguration.QueryParameters.Top = 1;
                }, cancellationToken: cancellationToken);
            });

            _logger.LogDebug("User {UserId} has accessible mailbox", userId);
            return true;
        }
        catch (ServiceException ex) when (
            ex.Message.Contains("mailbox is either inactive, soft-deleted, or is hosted on-premise", StringComparison.OrdinalIgnoreCase) ||
            ex.Message.Contains("does not have a mailbox", StringComparison.OrdinalIgnoreCase) ||
            ex.Message.Contains("on-premise", StringComparison.OrdinalIgnoreCase) ||
            ex.ResponseStatusCode == 400) // Bad Request often indicates mailbox issues
        {
            _logger.LogInformation("User {UserId} does not have accessible Exchange Online mailbox: {ErrorMessage}",
                userId, ex.Message);
            return false;
        }
        catch (ServiceException ex) when (ex.ResponseStatusCode == 403 || ex.ResponseStatusCode == 404)
        {
            _logger.LogInformation("User {UserId} mailbox not accessible (Status: {StatusCode}): {ErrorMessage}",
                userId, ex.ResponseStatusCode, ex.Message);
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error checking mailbox accessibility for user {UserId}, assuming mailbox exists", userId);
            return true; // Default to true to avoid skipping users due to temporary issues
        }
    }

    /// <summary>
    /// Extracts the $skip value from a URL query string
    /// </summary>
    private int? ExtractSkipValueFromUrl(string query)
    {
        if (string.IsNullOrEmpty(query)) return null;

        // Remove leading ? if present
        if (query.StartsWith("?")) query = query.Substring(1);

        // Split by & and look for $skip parameter
        var parameters = query.Split('&');
        foreach (var param in parameters)
        {
            var keyValue = param.Split('=');
            if (keyValue.Length == 2 && keyValue[0] == "%24skip")
            {
                if (int.TryParse(keyValue[1], out var skip))
                {
                    return skip;
                }
            }
        }

        return null;
    }
}
