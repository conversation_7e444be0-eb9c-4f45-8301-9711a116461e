<Project Sdk="Microsoft.NET.Sdk.BlazorWebAssembly">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="9.0.5" />
        <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.DevServer" Version="9.0.5" PrivateAssets="all" />
        <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="9.0.6" />
        <PackageReference Include="Microsoft.Graph" Version="5.56.0" />
        <PackageReference Include="Microsoft.Graph.Auth" Version="1.0.0-preview.7" />
        <PackageReference Include="Microsoft.Identity.Client" Version="4.61.3" />
    </ItemGroup>

    <ItemGroup>
      <Content Remove="Pages\Counter.razor" />
    </ItemGroup>

</Project>
