@page "/extractor"
@using System.Text.Json
@using System.Text.RegularExpressions
@using System.Text
@using Microsoft.AspNetCore.SignalR.Client
@inject IJSRuntime JSRuntime
@inject HttpClient HttpClient
@implements IAsyncDisposable

<PageTitle>Email Address Extractor</PageTitle>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <h1 class="display-4 text-center mb-4">
                <i class="fas fa-envelope-open-text me-3"></i>
                Email Address Extractor
            </h1>
            <p class="lead text-center text-muted mb-5">
                Extract and curate external email addresses from your Microsoft 365 tenant for newsletter purposes
            </p>
        </div>
    </div>

    @if (currentState == UIState.Configuration)
    {
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="fas fa-cog me-2"></i>Configuration</h4>
                    </div>
                    <div class="card-body">
                        <EditForm Model="config" OnValidSubmit="StartExtraction">
                            <DataAnnotationsValidator />
                            
                            <div class="accordion" id="configAccordion">
                                <!-- M365 Configuration Section -->
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="authHeader">
                                        <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                                data-bs-target="#authCollapse" aria-expanded="true" aria-controls="authCollapse">
                                            <i class="fas fa-key me-2"></i>Microsoft 365 Configuration
                                        </button>
                                    </h2>
                                    <div id="authCollapse" class="accordion-collapse collapse show"
                                         aria-labelledby="authHeader" data-bs-parent="#configAccordion">
                                        <div class="accordion-body">
                                            <div class="alert alert-warning">
                                                <h6><i class="fas fa-exclamation-triangle me-2"></i>Azure AD App Registration Required</h6>
                                                <p class="mb-0">You need to register an application in Azure AD with appropriate permissions to access Microsoft Graph API. Enter your app credentials below.</p>
                                            </div>

                                            <div class="mb-3">
                                                <label for="tenantId" class="form-label">Tenant ID <span class="text-danger">*</span></label>
                                                <InputText id="tenantId" class="form-control" @bind-Value="config.TenantId"
                                                          placeholder="xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx" />
                                                <div class="form-text">Your Azure AD tenant ID</div>
                                            </div>

                                            <div class="mb-3">
                                                <label for="clientId" class="form-label">Client ID <span class="text-danger">*</span></label>
                                                <InputText id="clientId" class="form-control" @bind-Value="config.ClientId"
                                                          placeholder="xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx" />
                                                <div class="form-text">Your Azure AD application (client) ID</div>
                                            </div>

                                            <div class="mb-3">
                                                <label for="clientSecret" class="form-label">Client Secret <span class="text-danger">*</span></label>
                                                <InputText id="clientSecret" class="form-control" type="password" @bind-Value="config.ClientSecret"
                                                          placeholder="Enter your client secret" />
                                                <div class="form-text">Your Azure AD application client secret</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Extraction Scope Section -->
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="scopeHeader">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                                data-bs-target="#scopeCollapse" aria-expanded="false" aria-controls="scopeCollapse">
                                            <i class="fas fa-users me-2"></i>Extraction Scope
                                        </button>
                                    </h2>
                                    <div id="scopeCollapse" class="accordion-collapse collapse" 
                                         aria-labelledby="scopeHeader" data-bs-parent="#configAccordion">
                                        <div class="accordion-body">
                                            <div class="mb-3">
                                                <InputRadioGroup @bind-Value="config.Scope">
                                                    <div class="form-check mb-2">
                                                        <InputRadio id="scopeEntire" class="form-check-input" Value="ExtractionScope.EntireTenant" />
                                                        <label class="form-check-label" for="scopeEntire">
                                                            Entire Tenant
                                                        </label>
                                                    </div>
                                                    <div class="form-check mb-2">
                                                        <InputRadio id="scopeGroup" class="form-check-input" Value="ExtractionScope.SpecificGroup" />
                                                        <label class="form-check-label" for="scopeGroup">
                                                            Specific M365 Group
                                                        </label>
                                                    </div>
                                                @if (config.Scope == ExtractionScope.SpecificGroup)
                                                {
                                                    <div class="ms-4 mb-3">
                                                        <label for="groupId" class="form-label">Group Name or ID</label>
                                                        <InputText id="groupId" class="form-control" @bind-Value="config.GroupId" />
                                                    </div>
                                                }
                                                    <div class="form-check">
                                                        <InputRadio id="scopeUsers" class="form-check-input" Value="ExtractionScope.SpecificUsers" />
                                                        <label class="form-check-label" for="scopeUsers">
                                                            Specific Users
                                                        </label>
                                                    </div>
                                                </InputRadioGroup>
                                                @if (config.Scope == ExtractionScope.SpecificUsers)
                                                {
                                                    <div class="ms-4 mb-3">
                                                        <label for="userList" class="form-label">User Principal Names (one per line)</label>
                                                        <InputTextArea id="userList" class="form-control" rows="4" @bind-Value="config.UserList" />
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Filtering Rules Section -->
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="filterHeader">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                                data-bs-target="#filterCollapse" aria-expanded="false" aria-controls="filterCollapse">
                                            <i class="fas fa-filter me-2"></i>Filtering Rules
                                        </button>
                                    </h2>
                                    <div id="filterCollapse" class="accordion-collapse collapse" 
                                         aria-labelledby="filterHeader" data-bs-parent="#configAccordion">
                                        <div class="accordion-body">
                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label for="startDate" class="form-label">Start Date (Optional)</label>
                                                    <InputDate id="startDate" class="form-control" @bind-Value="config.StartDate" />
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label for="endDate" class="form-label">End Date (Optional)</label>
                                                    <InputDate id="endDate" class="form-control" @bind-Value="config.EndDate" />
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <label for="companyDomains" class="form-label">Company Domains (comma-separated)</label>
                                                <InputText id="companyDomains" class="form-control" @bind-Value="config.CompanyDomains" 
                                                          placeholder="company.com,company.net" />
                                                <div class="form-text">Internal domains to exclude from results</div>
                                            </div>
                                            <div class="mb-3">
                                                <label for="genericPrefixes" class="form-label">Generic Prefixes to Exclude</label>
                                                <InputTextArea id="genericPrefixes" class="form-control" rows="3" @bind-Value="config.GenericPrefixes" />
                                                <div class="form-text">Email prefixes to exclude (comma-separated)</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="text-center mt-4">
                                <button type="submit" class="btn btn-primary btn-lg" disabled="@isProcessing">
                                    @if (isProcessing)
                                    {
                                        <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                        <span>Processing...</span>
                                    }
                                    else
                                    {
                                        <i class="fas fa-play me-2"></i>
                                        <span>Start Extraction</span>
                                    }
                                </button>
                            </div>
                        </EditForm>
                    </div>
                </div>
            </div>
        </div>
    }

    @if (currentState == UIState.Processing)
    {
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow">
                    <div class="card-header bg-info text-white">
                        <h4 class="mb-0"><i class="fas fa-cogs me-2"></i>Processing</h4>
                    </div>
                    <div class="card-body">
                        <!-- Current Stage -->
                        <div class="mb-4">
                            <h5>Current Stage</h5>
                            <div class="alert alert-info mb-2">
                                <i class="fas fa-info-circle me-2"></i>@currentStageDescription
                            </div>
                        </div>

                        @if (currentProgress != null)
                        {
                            <!-- User Progress -->
                            @if (currentProgress.TotalUsers > 0)
                            {
                                <div class="mb-4">
                                    <h5>User Progress</h5>
                                    <div class="progress mb-2">
                                        <div class="progress-bar" role="progressbar" style="width: @(GetUserProgressPercentage())%"
                                             aria-valuenow="@GetUserProgressPercentage()" aria-valuemin="0" aria-valuemax="100">
                                            @GetUserProgressPercentage()%
                                        </div>
                                    </div>
                                    <small class="text-muted">
                                        Processing user @currentProgress.CurrentUserIndex of @currentProgress.TotalUsers
                                        @if (!string.IsNullOrEmpty(currentProgress.CurrentUserName))
                                        {
                                            <span> - @currentProgress.CurrentUserName</span>
                                        }
                                    </small>
                                </div>
                            }

                            <!-- Folder Progress -->
                            @if (currentProgress.TotalFolders > 0)
                            {
                                <div class="mb-4">
                                    <h5>Folder Progress</h5>
                                    <div class="progress mb-2">
                                        <div class="progress-bar bg-info" role="progressbar" style="width: @(GetFolderProgressPercentage())%"
                                             aria-valuenow="@GetFolderProgressPercentage()" aria-valuemin="0" aria-valuemax="100">
                                            @GetFolderProgressPercentage()%
                                        </div>
                                    </div>
                                    <small class="text-muted">
                                        Processing folder @currentProgress.CurrentFolderIndex of @currentProgress.TotalFolders
                                        @if (!string.IsNullOrEmpty(currentProgress.CurrentFolderName))
                                        {
                                            <span> - @currentProgress.CurrentFolderName</span>
                                        }
                                    </small>
                                </div>
                            }

                            <!-- Email Progress -->
                            @if (currentProgress.TotalEmails > 0)
                            {
                                <div class="mb-4">
                                    <h5>Email Progress</h5>
                                    <div class="progress mb-2">
                                        <div class="progress-bar bg-success" role="progressbar" style="width: @(GetEmailProgressPercentage())%"
                                             aria-valuenow="@GetEmailProgressPercentage()" aria-valuemin="0" aria-valuemax="100">
                                            @GetEmailProgressPercentage()%
                                        </div>
                                    </div>
                                    <small class="text-muted">
                                        Processing email @currentProgress.ProcessedEmails of @currentProgress.TotalEmails in current folder
                                    </small>
                                </div>
                            }

                            <!-- Overall Statistics -->
                            <div class="mb-4">
                                <h5>Overall Statistics</h5>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="card bg-light">
                                            <div class="card-body text-center">
                                                <h6 class="card-title">Total Emails Processed</h6>
                                                <h4 class="text-primary">@currentProgress.TotalEmailsProcessedOverall</h4>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card bg-light">
                                            <div class="card-body text-center">
                                                <h6 class="card-title">Unique Emails Found</h6>
                                                <h4 class="text-success">@currentProgress.UniqueEmailsFound</h4>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="card bg-light">
                                            <div class="card-body text-center">
                                                <h6 class="card-title">Processing Rate</h6>
                                                <h4 class="text-info">@(GetProcessingRate())%</h4>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }

                        <div class="mb-4">
                            <h5>Live Log (Newest First)</h5>
                            <div class="border rounded p-3" style="height: 400px; overflow-y: auto; font-family: monospace; font-size: 0.9em; background-color: #f8f9fa;">
                                @foreach (var logEntry in logEntries.Take(100))
                                {
                                    <div class="mb-1">
                                        <span class="text-muted">[@logEntry.Timestamp.ToString("HH:mm:ss")]</span>
                                        <span class="@GetLogEntryClass(logEntry.Level)">@logEntry.Message</span>
                                    </div>
                                }
                            </div>
                        </div>

                        <div class="text-center">
                            <button type="button" class="btn btn-danger" @onclick="CancelProcessing">
                                <i class="fas fa-stop me-2"></i>Cancel Processing
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }

    @if (currentState == UIState.Results)
    {
        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header bg-success text-white">
                        <h4 class="mb-0"><i class="fas fa-check-circle me-2"></i>Results</h4>
                    </div>
                    <div class="card-body">
                        <!-- Summary Header -->
                        <div class="alert alert-info mb-4">
                            <h5 class="alert-heading">
                                <i class="fas fa-info-circle me-2"></i>Extraction Complete
                            </h5>
                            <p class="mb-0">
                                Found <strong>@filteredResults.Count(r => r.IsActive)</strong> unique external contacts
                                from <strong>@totalEmailsProcessed</strong> total emails processed
                            </p>
                        </div>

                        <!-- Interactive Controls -->
                        <div class="row mb-4">
                            <div class="col-md-4 mb-3">
                                <label for="searchBox" class="form-label">Search</label>
                                <input type="text" id="searchBox" class="form-control" @bind="searchTerm" @bind:event="oninput"
                                       placeholder="Filter by email or name..." />
                            </div>
                            <div class="col-md-8 mb-3">
                                <label class="form-label">Master Controls</label>
                                <div class="btn-group me-3" role="group">
                                    <button type="button" class="btn btn-outline-primary" @onclick="SelectAll">
                                        <i class="fas fa-check-square me-1"></i>Select All
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" @onclick="DeselectAll">
                                        <i class="fas fa-square me-1"></i>Deselect All
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Export Format</label>
                                <div class="btn-group d-block" role="group">
                                    <input type="radio" class="btn-check" name="exportFormat" id="emailOnly" @onchange="() => exportFormat = ExportFormat.EmailOnly" checked="@(exportFormat == ExportFormat.EmailOnly)" />
                                    <label class="btn btn-outline-primary" for="emailOnly">Email Only</label>

                                    <input type="radio" class="btn-check" name="exportFormat" id="nameEmail" @onchange="() => exportFormat = ExportFormat.NameEmail" checked="@(exportFormat == ExportFormat.NameEmail)" />
                                    <label class="btn btn-outline-primary" for="nameEmail">Name &lt;email&gt;</label>

                                    <input type="radio" class="btn-check" name="exportFormat" id="nameOnly" @onchange="() => exportFormat = ExportFormat.NameOnly" checked="@(exportFormat == ExportFormat.NameOnly)" />
                                    <label class="btn btn-outline-primary" for="nameOnly">Name Only</label>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Export Actions</label>
                                <div class="btn-group d-block" role="group">
                                    <button type="button" class="btn btn-success" @onclick="CopyToClipboard">
                                        <i class="fas fa-copy me-1"></i>Copy to Clipboard
                                    </button>
                                    <button type="button" class="btn btn-primary" @onclick="DownloadCsv">
                                        <i class="fas fa-download me-1"></i>Download CSV
                                    </button>
                                    <button type="button" class="btn btn-info" @onclick="DownloadExcel">
                                        <i class="fas fa-file-excel me-1"></i>Download Excel
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Results Table -->
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th style="width: 60px;">
                                            <input type="checkbox" class="form-check-input" @onchange="ToggleAllResults"
                                                   checked="@(filteredResults.Any() && filteredResults.All(r => r.IsActive))" />
                                        </th>
                                        <th style="cursor: pointer;" @onclick="() => SortResults(nameof(EmailResult.DisplayName))">
                                            Display Name
                                            @if (sortColumn == nameof(EmailResult.DisplayName))
                                            {
                                                <i class="fas fa-sort-@(sortAscending ? "up" : "down") ms-1"></i>
                                            }
                                        </th>
                                        <th style="cursor: pointer;" @onclick="() => SortResults(nameof(EmailResult.EmailAddress))">
                                            Email Address
                                            @if (sortColumn == nameof(EmailResult.EmailAddress))
                                            {
                                                <i class="fas fa-sort-@(sortAscending ? "up" : "down") ms-1"></i>
                                            }
                                        </th>
                                        <th style="cursor: pointer;" @onclick="() => SortResults(nameof(EmailResult.CollectedFrom))">
                                            Source
                                            @if (sortColumn == nameof(EmailResult.CollectedFrom))
                                            {
                                                <i class="fas fa-sort-@(sortAscending ? "up" : "down") ms-1"></i>
                                            }
                                        </th>
                                        <th style="cursor: pointer;" @onclick="() => SortResults(nameof(EmailResult.MentionCount))">
                                            Mentions
                                            @if (sortColumn == nameof(EmailResult.MentionCount))
                                            {
                                                <i class="fas fa-sort-@(sortAscending ? "up" : "down") ms-1"></i>
                                            }
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var result in filteredResults)
                                    {
                                        <tr key="@result.Id">
                                            <td>
                                                <input type="checkbox" class="form-check-input" @bind="result.IsActive" />
                                            </td>
                                            <td>@(result.DisplayName ?? "N/A")</td>
                                            <td>@result.EmailAddress</td>
                                            <td>@result.CollectedFrom</td>
                                            <td>
                                                <span class="badge bg-secondary">@result.MentionCount</span>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        @if (!filteredResults.Any())
                        {
                            <div class="text-center py-5">
                                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No results found</h5>
                                <p class="text-muted">Try adjusting your search criteria or extraction settings.</p>
                            </div>
                        }

                        <div class="text-center mt-4">
                            <button type="button" class="btn btn-secondary" @onclick="ResetToConfiguration">
                                <i class="fas fa-arrow-left me-2"></i>Back to Configuration
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@if (!string.IsNullOrEmpty(errorMessage))
{
    <div class="alert alert-danger alert-dismissible fade show mt-3" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        @errorMessage
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close" @onclick="() => errorMessage = string.Empty"></button>
    </div>
}

@code {
    // Data Models
    public record EmailResult
    {
        public string EmailAddress { get; init; } = string.Empty;
        public string? DisplayName { get; init; }
        public int MentionCount { get; set; } = 1;
        public string CollectedFrom { get; init; } = string.Empty;
        public bool IsActive { get; set; } = true;
        public string Id { get; init; } = Guid.NewGuid().ToString();
    }

    public record ConfigurationData
    {
        public ExtractionScope Scope { get; set; } = ExtractionScope.EntireTenant;
        public string? GroupId { get; set; }
        public string? UserList { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string CompanyDomains { get; set; } = string.Empty;
        public string GenericPrefixes { get; set; } = "noreply,no-reply,support,help,contact,info,admin,marketing,sales,hr,finance";

        // M365 Configuration
        public string TenantId { get; set; } = string.Empty;
        public string ClientId { get; set; } = string.Empty;
        public string ClientSecret { get; set; } = string.Empty;
    }

    public record ExtractionRequest(
        ScopeType Scope,
        string? ScopeIdentifier,
        DateTime? StartDate,
        DateTime? EndDate,
        string InternalDomains,
        string GenericPrefixes,
        M365Configuration M365Config
    );

    public record M365Configuration(
        string TenantId,
        string ClientId,
        string ClientSecret
    )
    {
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(TenantId) &&
                   !string.IsNullOrWhiteSpace(ClientId) &&
                   !string.IsNullOrWhiteSpace(ClientSecret);
        }
    }

    public enum ExtractionScope
    {
        EntireTenant,
        SpecificGroup,
        SpecificUsers
    }

    public enum ScopeType
    {
        All,
        Group,
        SpecificUsers
    }

    public record ProgressUpdate(
        ProgressStage Stage,
        int TotalUsers,
        int CurrentUserIndex,
        string? CurrentUserName,
        int TotalFolders,
        int CurrentFolderIndex,
        string? CurrentFolderName,
        int TotalEmails,
        int ProcessedEmails,
        int TotalEmailsProcessedOverall,
        int UniqueEmailsFound
    );

    public enum ProgressStage
    {
        Initializing,
        FetchingUsers,
        ProcessingUsers,
        ProcessingFolders,
        ProcessingEmails,
        Completed,
        Error
    }

    public enum UIState
    {
        Configuration,
        Processing,
        Results
    }

    public enum ExportFormat
    {
        EmailOnly,
        NameEmail,
        NameOnly
    }

    public record LogEntry
    {
        public DateTime Timestamp { get; init; } = DateTime.Now;
        public string Message { get; init; } = string.Empty;
        public LogLevel Level { get; init; } = LogLevel.Info;
    }

    public enum LogLevel
    {
        Info,
        Warning,
        Error,
        Success
    }

    // State Variables
    private UIState currentState = UIState.Configuration;
    private ConfigurationData config = new();
    private bool isProcessing = false;
    private string errorMessage = string.Empty;

    // Processing State Variables
    private ProgressUpdate? currentProgress;
    private string currentStageDescription = string.Empty;
    private List<LogEntry> logEntries = new();
    private CancellationTokenSource? cancellationTokenSource;

    // Results State Variables
    private Dictionary<string, EmailResult> emailResults = new();
    private List<EmailResult> filteredResults = new();
    private string _searchTerm = string.Empty;
    private string searchTerm
    {
        get => _searchTerm;
        set
        {
            _searchTerm = value;
            UpdateFilteredResults();
        }
    }
    private string sortColumn = nameof(EmailResult.EmailAddress);
    private bool sortAscending = true;
    private ExportFormat exportFormat = ExportFormat.EmailOnly;
    private int totalEmailsProcessed = 0;

    // SignalR
    private HubConnection? hubConnection;
    private const string API_BASE_URL = "https://localhost:7012"; // Updated to use HTTPS to fix CORS

    protected override async Task OnInitializedAsync()
    {
        // Initialize with default values
        UpdateFilteredResults();

        // Initialize SignalR connection
        hubConnection = new HubConnectionBuilder()
            .WithUrl($"{API_BASE_URL}/progressHub")
            .Build();

        hubConnection.On<string>("LogMessage", (message) =>
        {
            AddLogEntry(message, LogLevel.Info);
            InvokeAsync(StateHasChanged);
        });

        hubConnection.On<ProgressUpdate>("ProgressUpdate", (progress) =>
        {
            currentProgress = progress;
            UpdateStageDescription();
            InvokeAsync(StateHasChanged);
        });

        await hubConnection.StartAsync();
    }

    private async Task StartExtraction()
    {
        try
        {
            errorMessage = string.Empty;

            // Validate M365 configuration
            var m365Config = new M365Configuration(config.TenantId, config.ClientId, config.ClientSecret);
            if (!m365Config.IsValid())
            {
                errorMessage = "Please provide valid Microsoft 365 configuration (Tenant ID, Client ID, and Client Secret are required).";
                return;
            }

            isProcessing = true;
            currentState = UIState.Processing;

            // Reset state
            emailResults.Clear();
            logEntries.Clear();
            currentProgress = null;
            currentStageDescription = string.Empty;
            totalEmailsProcessed = 0;

            cancellationTokenSource = new CancellationTokenSource();

            AddLogEntry("Starting email extraction process...", LogLevel.Info);
            StateHasChanged();

            // Create API request
            var request = new ExtractionRequest(
                Scope: config.Scope switch
                {
                    ExtractionScope.EntireTenant => ScopeType.All,
                    ExtractionScope.SpecificGroup => ScopeType.Group,
                    ExtractionScope.SpecificUsers => ScopeType.SpecificUsers,
                    _ => ScopeType.All
                },
                ScopeIdentifier: config.Scope switch
                {
                    ExtractionScope.SpecificGroup => config.GroupId,
                    ExtractionScope.SpecificUsers => config.UserList,
                    _ => null
                },
                StartDate: config.StartDate,
                EndDate: config.EndDate,
                InternalDomains: config.CompanyDomains,
                GenericPrefixes: config.GenericPrefixes,
                M365Config: new M365Configuration(config.TenantId, config.ClientId, config.ClientSecret)
            );

            // Call API
            HttpClient.BaseAddress = new Uri(API_BASE_URL);
            var response = await HttpClient.PostAsJsonAsync("/api/extraction/extract", request, cancellationTokenSource.Token);

            if (response.IsSuccessStatusCode)
            {
                var results = await response.Content.ReadFromJsonAsync<List<EmailResult>>(cancellationToken: cancellationTokenSource.Token);

                if (results != null)
                {
                    // Convert API results to local format
                    emailResults.Clear();
                    foreach (var result in results)
                    {
                        emailResults[result.EmailAddress] = result;
                    }

                    AddLogEntry($"Extraction completed! Found {emailResults.Count} unique external email addresses.", LogLevel.Success);

                    // Transition to results
                    currentState = UIState.Results;
                    UpdateFilteredResults();
                }
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                throw new Exception($"API call failed: {response.StatusCode} - {errorContent}");
            }
        }
        catch (OperationCanceledException)
        {
            AddLogEntry("Extraction was cancelled by user.", LogLevel.Warning);
            currentState = UIState.Configuration;
        }
        catch (Exception ex)
        {
            errorMessage = $"Error during extraction: {ex.Message}";
            AddLogEntry($"Error: {ex.Message}", LogLevel.Error);
            currentState = UIState.Configuration;
        }
        finally
        {
            isProcessing = false;
            StateHasChanged();
        }
    }

    // Removed old demo methods - now using API backend

    // Old demo methods removed - using API backend

    // Old demo processing methods removed

    private void UpdateStageDescription()
    {
        if (currentProgress == null)
        {
            currentStageDescription = "Initializing...";
            return;
        }

        currentStageDescription = currentProgress.Stage switch
        {
            ProgressStage.Initializing => "Initializing extraction process...",
            ProgressStage.FetchingUsers => "Fetching users from Microsoft 365...",
            ProgressStage.ProcessingUsers => $"Processing users ({currentProgress.CurrentUserIndex} of {currentProgress.TotalUsers})",
            ProgressStage.ProcessingFolders => $"Processing folders for {currentProgress.CurrentUserName} ({currentProgress.CurrentFolderIndex} of {currentProgress.TotalFolders})",
            ProgressStage.ProcessingEmails => $"Processing emails in {currentProgress.CurrentFolderName} ({currentProgress.ProcessedEmails} of {currentProgress.TotalEmails})",
            ProgressStage.Completed => "Extraction completed successfully!",
            ProgressStage.Error => "An error occurred during extraction.",
            _ => "Processing..."
        };
    }

    private int GetUserProgressPercentage()
    {
        if (currentProgress == null || currentProgress.TotalUsers == 0) return 0;
        return (int)((double)currentProgress.CurrentUserIndex / currentProgress.TotalUsers * 100);
    }

    private int GetFolderProgressPercentage()
    {
        if (currentProgress == null || currentProgress.TotalFolders == 0) return 0;
        return (int)((double)currentProgress.CurrentFolderIndex / currentProgress.TotalFolders * 100);
    }

    private int GetEmailProgressPercentage()
    {
        if (currentProgress == null || currentProgress.TotalEmails == 0) return 0;
        return (int)((double)currentProgress.ProcessedEmails / currentProgress.TotalEmails * 100);
    }

    private double GetProcessingRate()
    {
        if (currentProgress == null || currentProgress.TotalEmailsProcessedOverall == 0) return 0;
        return Math.Round((double)currentProgress.UniqueEmailsFound / currentProgress.TotalEmailsProcessedOverall * 100, 1);
    }

    // UI Event Handlers
    private void CancelProcessing()
    {
        try
        {
            cancellationTokenSource?.Cancel();
            AddLogEntry("Cancellation requested by user", LogLevel.Warning);
        }
        catch (Exception ex)
        {
            AddLogEntry($"Error during cancellation: {ex.Message}", LogLevel.Error);
        }
    }

    private void ResetToConfiguration()
    {
        currentState = UIState.Configuration;
        errorMessage = string.Empty;
        emailResults.Clear();
        logEntries.Clear();
    }

    private void AddLogEntry(string message, LogLevel level)
    {
        logEntries.Insert(0, new LogEntry { Message = message, Level = level, Timestamp = DateTime.Now });
        if (logEntries.Count > 2000) // Keep only last 2000 entries
        {
            logEntries.RemoveAt(logEntries.Count - 1);
        }
    }

    private string GetLogEntryClass(LogLevel level)
    {
        return level switch
        {
            LogLevel.Error => "text-danger",
            LogLevel.Warning => "text-warning",
            LogLevel.Success => "text-success",
            _ => "text-dark"
        };
    }

    // Results Management
    private void UpdateFilteredResults()
    {
        var results = emailResults.Values.AsEnumerable();

        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            var search = searchTerm.ToLowerInvariant();
            results = results.Where(r =>
                r.EmailAddress.Contains(search) ||
                (r.DisplayName?.ToLowerInvariant().Contains(search) ?? false));
        }

        // Apply sorting
        results = sortColumn switch
        {
            nameof(EmailResult.DisplayName) => sortAscending
                ? results.OrderBy(r => r.DisplayName ?? string.Empty)
                : results.OrderByDescending(r => r.DisplayName ?? string.Empty),
            nameof(EmailResult.EmailAddress) => sortAscending
                ? results.OrderBy(r => r.EmailAddress)
                : results.OrderByDescending(r => r.EmailAddress),
            nameof(EmailResult.CollectedFrom) => sortAscending
                ? results.OrderBy(r => r.CollectedFrom)
                : results.OrderByDescending(r => r.CollectedFrom),
            nameof(EmailResult.MentionCount) => sortAscending
                ? results.OrderBy(r => r.MentionCount)
                : results.OrderByDescending(r => r.MentionCount),
            _ => results.OrderBy(r => r.EmailAddress)
        };

        filteredResults = results.ToList();
        StateHasChanged();
    }

    private void SortResults(string column)
    {
        if (sortColumn == column)
        {
            sortAscending = !sortAscending;
        }
        else
        {
            sortColumn = column;
            sortAscending = true;
        }
        UpdateFilteredResults();
    }

    private void SelectAll()
    {
        foreach (var result in filteredResults)
        {
            result.IsActive = true;
        }
        StateHasChanged();
    }

    private void DeselectAll()
    {
        foreach (var result in filteredResults)
        {
            result.IsActive = false;
        }
        StateHasChanged();
    }

    private void ToggleAllResults(ChangeEventArgs e)
    {
        var isChecked = (bool)(e.Value ?? false);
        foreach (var result in filteredResults)
        {
            result.IsActive = isChecked;
        }
        StateHasChanged();
    }

    // Export Functions
    private async Task CopyToClipboard()
    {
        try
        {
            var activeResults = filteredResults.Where(r => r.IsActive).ToList();
            if (!activeResults.Any())
            {
                errorMessage = "No active results to copy";
                return;
            }

            var content = FormatResultsForExport(activeResults);
            await JSRuntime.InvokeVoidAsync("navigator.clipboard.writeText", content);

            AddLogEntry($"Copied {activeResults.Count} email addresses to clipboard", LogLevel.Success);
        }
        catch (Exception ex)
        {
            errorMessage = $"Failed to copy to clipboard: {ex.Message}";
        }
    }

    private async Task DownloadCsv()
    {
        try
        {
            var activeResults = filteredResults.Where(r => r.IsActive).ToList();
            if (!activeResults.Any())
            {
                errorMessage = "No active results to download";
                return;
            }

            var csv = new StringBuilder();
            csv.AppendLine("Email Address,Display Name,Source,Mentions");

            foreach (var result in activeResults)
            {
                csv.AppendLine($"\"{result.EmailAddress}\",\"{result.DisplayName ?? ""}\",\"{result.CollectedFrom}\",{result.MentionCount}");
            }

            var fileName = $"email_addresses_{DateTime.Now:yyyyMMdd_HHmmss}.csv";
            await DownloadFile(fileName, csv.ToString(), "text/csv");

            AddLogEntry($"Downloaded {activeResults.Count} email addresses as CSV", LogLevel.Success);
        }
        catch (Exception ex)
        {
            errorMessage = $"Failed to download CSV: {ex.Message}";
        }
    }

    private async Task DownloadExcel()
    {
        try
        {
            var activeResults = filteredResults.Where(r => r.IsActive).ToList();
            if (!activeResults.Any())
            {
                errorMessage = "No active results to download";
                return;
            }

            // Create a simple Excel-compatible CSV with proper formatting
            var csv = new StringBuilder();
            csv.AppendLine("Email Address\tDisplay Name\tSource\tMentions");

            foreach (var result in activeResults)
            {
                csv.AppendLine($"{result.EmailAddress}\t{result.DisplayName ?? ""}\t{result.CollectedFrom}\t{result.MentionCount}");
            }

            var fileName = $"email_addresses_{DateTime.Now:yyyyMMdd_HHmmss}.xls";
            await DownloadFile(fileName, csv.ToString(), "application/vnd.ms-excel");

            AddLogEntry($"Downloaded {activeResults.Count} email addresses as Excel", LogLevel.Success);
        }
        catch (Exception ex)
        {
            errorMessage = $"Failed to download Excel: {ex.Message}";
        }
    }

    private string FormatResultsForExport(List<EmailResult> results)
    {
        return exportFormat switch
        {
            ExportFormat.EmailOnly => string.Join("\n", results.Select(r => r.EmailAddress)),
            ExportFormat.NameEmail => string.Join("\n", results.Select(r =>
                string.IsNullOrWhiteSpace(r.DisplayName) ? r.EmailAddress : $"{r.DisplayName} <{r.EmailAddress}>")),
            ExportFormat.NameOnly => string.Join("\n", results.Select(r => r.DisplayName ?? r.EmailAddress)),
            _ => string.Join("\n", results.Select(r => r.EmailAddress))
        };
    }

    private async Task DownloadFile(string fileName, string content, string contentType)
    {
        var bytes = Encoding.UTF8.GetBytes(content);
        var base64 = Convert.ToBase64String(bytes);

        await JSRuntime.InvokeVoidAsync("downloadFile", fileName, base64, contentType);
    }

    // Search functionality - handled by the searchTerm field above

    public async ValueTask DisposeAsync()
    {
        if (hubConnection is not null)
        {
            await hubConnection.DisposeAsync();
        }
    }
}

<script>
    window.downloadFile = (fileName, base64Content, contentType) => {
        const link = document.createElement('a');
        link.href = `data:${contentType};base64,${base64Content}`;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };
</script>
